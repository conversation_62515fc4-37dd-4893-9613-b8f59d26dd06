from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from blockchain_security import blockchain_security

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    password_hash = db.Column(db.String(500), nullable=False)  # Increased size for encrypted hash
    blockchain_hash = db.Column(db.String(64))  # Blockchain hash for user data
    is_admin = db.Column(db.<PERSON>, default=False)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    posts = db.relationship('Post', backref='author', lazy=True)

    def set_password(self, password):
        # Use blockchain security for enhanced password encryption
        self.password_hash = blockchain_security.encrypt_password(password)
        # Create blockchain hash for user data integrity
        self.blockchain_hash = blockchain_security.create_user_hash(
            self.username, self.email
        )

    def check_password(self, password):
        # Use blockchain security for password verification
        return blockchain_security.verify_password(password, self.password_hash)

    def verify_integrity(self):
        """Verify user data integrity using blockchain hash"""
        if not self.blockchain_hash:
            return False
        return blockchain_security.verify_blockchain_hash(
            f"{self.username}:{self.email}", self.blockchain_hash
        )

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    slug = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    color = db.Column(db.String(7), default='#ffc107')  # Hex color
    icon = db.Column(db.String(50), default='fas fa-book')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    posts = db.relationship('Post', backref='category', lazy=True)

class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    slug = db.Column(db.String(200), nullable=False, unique=True)
    content = db.Column(db.Text, nullable=False)
    summary = db.Column(db.String(300))
    featured_image = db.Column(db.String(255))
    meta_title = db.Column(db.String(60))  # SEO title
    meta_description = db.Column(db.String(160))  # SEO description
    keywords = db.Column(db.String(255))  # SEO keywords
    blockchain_hash = db.Column(db.String(64))  # Blockchain hash for post integrity
    content_signature = db.Column(db.String(500))  # Digital signature for content
    is_published = db.Column(db.Boolean, default=True)
    is_featured = db.Column(db.Boolean, default=False)
    view_count = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=False)
    comments = db.relationship('Comment', backref='post', lazy=True, cascade='all, delete-orphan')

    def create_blockchain_security(self):
        """Create blockchain hash and digital signature for post"""
        self.blockchain_hash = blockchain_security.create_post_hash(
            self.title, self.content, self.user_id
        )
        # Create digital signature for content integrity
        content_data = f"{self.title}:{self.content}"
        self.content_signature = blockchain_security.create_digital_signature(content_data)

    def verify_integrity(self):
        """Verify post data integrity"""
        if not self.blockchain_hash or not self.content_signature:
            return False

        # Verify blockchain hash
        hash_valid = blockchain_security.verify_blockchain_hash(
            f"{self.title}:{self.content[:100]}:{self.user_id}", self.blockchain_hash
        )

        # Verify digital signature
        content_data = f"{self.title}:{self.content}"
        signature_valid = blockchain_security.verify_digital_signature(
            content_data, self.content_signature
        )

        return hash_valid and signature_valid

class Comment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    author_name = db.Column(db.String(100), nullable=False)
    blockchain_hash = db.Column(db.String(64))  # Blockchain hash for comment integrity
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    post_id = db.Column(db.Integer, db.ForeignKey('post.id'), nullable=False)
    is_approved = db.Column(db.Boolean, default=False)

    def create_blockchain_security(self):
        """Create blockchain hash for comment"""
        self.blockchain_hash = blockchain_security.create_comment_hash(
            self.content, self.author_name, self.post_id
        )

    def verify_integrity(self):
        """Verify comment data integrity"""
        if not self.blockchain_hash:
            return False
        return blockchain_security.verify_blockchain_hash(
            f"{self.content}:{self.author_name}:{self.post_id}", self.blockchain_hash
        )


class BlockchainLog(db.Model):
    """Store blockchain security logs for admin actions"""
    id = db.Column(db.Integer, primary_key=True)
    action = db.Column(db.String(100), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    details = db.Column(db.Text)  # JSON string of action details
    blockchain_hash = db.Column(db.String(64), nullable=False)
    digital_signature = db.Column(db.String(500), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    def create_from_action(self, action, user_id, details=None):
        """Create blockchain log entry"""
        log_entry = blockchain_security.create_admin_action_log(action, user_id, details)

        self.action = action
        self.user_id = user_id
        self.details = str(details) if details else None
        self.blockchain_hash = log_entry["hash"]
        self.digital_signature = log_entry["signature"]

    def verify_integrity(self):
        """Verify log entry integrity"""
        log_entry = {
            "data": {
                "action": self.action,
                "user_id": self.user_id,
                "timestamp": self.timestamp.isoformat(),
                "details": eval(self.details) if self.details else {}
            },
            "hash": self.blockchain_hash,
            "signature": self.digital_signature
        }
        return blockchain_security.verify_admin_action_log(log_entry)
