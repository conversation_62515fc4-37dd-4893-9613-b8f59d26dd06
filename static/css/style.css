/* Modern Buddhist Blog Platform Styles */

:root {
    --primary-color: #ffc107;
    --secondary-color: #6c757d;
    --accent-color: #fd7e14;
    --text-dark: #212529;
    --text-muted: #6c757d;
    --bg-light: #f8f9fa;
    --border-color: #dee2e6;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --border-radius: 0.75rem;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Noto Serif', serif;
    font-weight: 600;
}

.fw-bold {
    font-weight: 700 !important;
}

/* Top Bar */
.top-bar {
    font-size: 0.875rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

/* Navigation */
.navbar {
    padding: 0.5rem 0;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
}

.navbar.scrolled {
    padding: 0.25rem 0;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.navbar-brand h4 {
    font-family: 'Noto Serif', serif;
    color: var(--text-dark) !important;
    font-size: 1.25rem;
    margin-bottom: 0;
}

.navbar-brand small {
    font-size: 0.75rem;
}

.logo-container i {
    font-size: 1.5rem;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.nav-link {
    font-weight: 500;
    color: var(--text-dark) !important;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-color) !important;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

/* Hero Section */
.hero-section {
    padding: 6rem 0 4rem;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(253, 126, 20, 0.1) 100%);
    border-radius: 50% 0 0 50%;
    z-index: 1;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.1; }
    50% { opacity: 0.2; }
}

.hero-content {
    position: relative;
    z-index: 2;
    animation: fadeInLeft 1s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-content h1 {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-content p {
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-actions {
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-stats {
    animation: fadeInUp 1s ease-out 0.8s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.min-vh-75 {
    min-height: 75vh;
}

.hero-actions .btn {
    border-radius: 50px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.hero-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.hero-actions .btn:hover::before {
    left: 100%;
}

.hero-actions .btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.hero-stats .stat-item {
    text-align: center;
    padding: 1rem 0;
    transition: transform 0.3s ease;
}

.hero-stats .stat-item:hover {
    transform: translateY(-5px);
}

.hero-stats .stat-item h4 {
    animation: countUp 2s ease-out 1s both;
}

@keyframes countUp {
    from { opacity: 0; transform: scale(0.5); }
    to { opacity: 1; transform: scale(1); }
}

/* Floating Elements */
.floating-elements {
    position: relative;
    height: 400px;
    animation: fadeInRight 1s ease-out 0.5s both;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    border: 1px solid rgba(255, 193, 7, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    animation: float 6s ease-in-out infinite, slideInScale 1s ease-out both;
}

.floating-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.floating-card:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s, 0.7s;
    background: linear-gradient(135deg, #fff 0%, #fff9e6 100%);
}

.floating-card:nth-child(2) {
    top: 50%;
    right: 20%;
    animation-delay: 2s, 0.9s;
    background: linear-gradient(135deg, #fff 0%, #e6f7ff 100%);
}

.floating-card:nth-child(3) {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s, 1.1s;
    background: linear-gradient(135deg, #fff 0%, #f0fff0 100%);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(1deg); }
    50% { transform: translateY(-20px) rotate(0deg); }
    75% { transform: translateY(-10px) rotate(-1deg); }
}

@keyframes slideInScale {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.floating-card i {
    transition: all 0.3s ease;
}

.floating-card:hover i {
    transform: scale(1.2) rotate(10deg);
}

.floating-card h5 {
    transition: color 0.3s ease;
}

.floating-card:hover h5 {
    color: var(--primary-color);
}

/* Post Cards */
.post-card-modern {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(30px);
}

.post-card-modern.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.post-card-hover {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.post-card-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
    z-index: 1;
}

.post-card-hover:hover::before {
    left: 100%;
}

.post-card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

/* Featured Image */
.card-img-container {
    position: relative;
    overflow: hidden;
}

.card-img-top {
    transition: transform 0.4s ease;
}

.post-card-hover:hover .card-img-top {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, transparent 50%);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1rem;
}

.category-badge {
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.featured-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.category-tag {
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Post Content */
.post-title a {
    transition: color 0.3s ease;
}

.post-title a:hover {
    color: var(--primary-color) !important;
}

.post-excerpt {
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.author-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
}

.post-meta .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 50px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.post-meta .badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-read-more {
    transition: all 0.3s ease;
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
}

.btn-read-more:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

/* Sidebar Widgets */
.widget .card {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.widget .card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.category-list .category-item {
    display: block;
    padding: 0.75rem 0;
    color: var(--text-dark);
    text-decoration: none;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.category-list .category-item:last-child {
    border-bottom: none;
}

.category-list .category-item:hover {
    color: var(--primary-color);
    padding-left: 0.5rem;
}

.activity-list .activity-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-list .activity-item:last-child {
    border-bottom: none;
}

/* ===== BACK TO TOP & HOME BUTTONS ===== */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.floating-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    opacity: 0;
    transform: translateY(20px) scale(0.8);
    animation: slideInUp 0.5s ease-out forwards;
}

.floating-btn.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.floating-btn:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: 0 8px 30px rgba(0,0,0,0.25);
    color: white;
}

.back-to-top {
    background: linear-gradient(135deg, #ffc107, #ff9800);
    animation-delay: 0.1s;
}

.back-to-top:hover {
    background: linear-gradient(135deg, #ff9800, #f57c00);
}

.home-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    animation-delay: 0.2s;
}

.home-btn:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Pulse animation for floating buttons */
.floating-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: inherit;
    animation: pulse-ring 2s infinite;
    opacity: 0;
}

@keyframes pulse-ring {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

/* ===== ENHANCED POST ANIMATIONS ===== */
.post-card-modern {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(30px);
}

.post-card-modern.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered animation for posts */
.post-card-modern:nth-child(1) { animation-delay: 0.1s; }
.post-card-modern:nth-child(2) { animation-delay: 0.2s; }
.post-card-modern:nth-child(3) { animation-delay: 0.3s; }
.post-card-modern:nth-child(4) { animation-delay: 0.4s; }
.post-card-modern:nth-child(5) { animation-delay: 0.5s; }
.post-card-modern:nth-child(6) { animation-delay: 0.6s; }

/* Enhanced hover effects for post cards */
.post-card-hover {
    position: relative;
    overflow: hidden;
}

.post-card-hover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,193,7,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.post-card-hover:hover::after {
    transform: translateX(100%);
}

/* Loading animation for images */
.card-img-top {
    transition: all 0.4s ease;
    filter: brightness(1);
}

.post-card-hover:hover .card-img-top {
    filter: brightness(1.1);
    transform: scale(1.05);
}

/* ===== CONTENT EDITOR ENHANCEMENTS ===== */
.content-editor-toolbar {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 0.375rem 0.375rem 0 0;
    padding: 0.5rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.editor-btn {
    padding: 0.375rem 0.75rem;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.editor-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.editor-btn.active {
    background: #ffc107;
    border-color: #ffc107;
    color: white;
}

/* ===== RESPONSIVE FLOATING BUTTONS ===== */
@media (max-width: 768px) {
    .floating-buttons {
        bottom: 20px;
        right: 20px;
        gap: 10px;
    }

    .floating-btn {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .floating-buttons {
        bottom: 15px;
        right: 15px;
    }

    .floating-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }
}

/* ===== VIDEO CONTAINER ===== */
.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    margin: 2rem 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* ===== ENHANCED ANIMATIONS ===== */
@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Animation utility classes */
.slide-in-left {
    animation: slideInFromLeft 0.6s ease-out forwards;
}

.slide-in-right {
    animation: slideInFromRight 0.6s ease-out forwards;
}

.rotate-in {
    animation: rotateIn 0.8s ease-out forwards;
}

.bounce-in {
    animation: bounceIn 0.8s ease-out forwards;
}

/* ===== CONTENT ENHANCEMENTS ===== */
.post-content-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

.post-content-body h2 {
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 3px solid #ffc107;
    color: #1a1a1a;
    font-weight: 700;
}

.post-content-body h3 {
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
    font-weight: 600;
}

.post-content-body blockquote {
    border-left: 4px solid #ffc107;
    background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
    padding: 1.5rem 2rem;
    margin: 2rem 0;
    border-radius: 0 0.5rem 0.5rem 0;
    font-style: italic;
    position: relative;
}

.post-content-body blockquote::before {
    content: '"';
    font-size: 4rem;
    color: #ffc107;
    position: absolute;
    top: -0.5rem;
    left: 1rem;
    opacity: 0.3;
    font-family: serif;
}

.post-content-body code {
    background: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9em;
    color: #e83e8c;
    border: 1px solid #e9ecef;
}

.post-content-body pre {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.post-content-body pre code {
    background: none;
    padding: 0;
    border: none;
    color: #333;
}

/* ===== LOADING ANIMATIONS ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* ===== SCROLL ANIMATIONS ===== */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* ===== ENHANCED EDITOR STYLES ===== */
.content-editor-toolbar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 0.375rem 0.375rem 0 0;
    padding: 0.75rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.editor-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
    color: #495057;
    position: relative;
    overflow: hidden;
}

.editor-btn:hover {
    background: #ffc107;
    border-color: #ffc107;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.editor-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.editor-btn.active {
    background: #ffc107;
    border-color: #ffc107;
    color: white;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

/* Button group styling */
.btn-group .editor-btn:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .editor-btn:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group .editor-btn:not(:first-child):not(:last-child) {
    border-radius: 0;
}

.btn-group .editor-btn + .editor-btn {
    border-left: none;
}

/* Preview area styling */
#content-preview {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0 0 0.375rem 0.375rem;
    padding: 1.5rem;
    min-height: 400px;
    font-family: 'Inter', sans-serif;
    line-height: 1.7;
    color: #333;
}

#content-preview h1, #content-preview h2, #content-preview h3, #content-preview h4 {
    font-family: 'Noto Serif', serif;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

#content-preview h2 {
    border-bottom: 3px solid #ffc107;
    padding-bottom: 0.5rem;
}

#content-preview blockquote {
    border-left: 4px solid #ffc107;
    background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
    padding: 1.5rem 2rem;
    margin: 2rem 0;
    border-radius: 0 0.5rem 0.5rem 0;
    font-style: italic;
    position: relative;
}

#content-preview blockquote::before {
    content: '"';
    font-size: 4rem;
    color: #ffc107;
    position: absolute;
    top: -0.5rem;
    left: 1rem;
    opacity: 0.3;
    font-family: serif;
}

#content-preview code {
    background: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9em;
    color: #e83e8c;
    border: 1px solid #e9ecef;
}

#content-preview pre {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
    overflow-x: auto;
    margin: 1.5rem 0;
}

#content-preview pre code {
    background: none;
    padding: 0;
    border: none;
    color: #333;
}

/* Image gallery styling */
.image-gallery {
    margin: 2rem 0;
}

.image-gallery img {
    transition: transform 0.3s ease;
    cursor: pointer;
}

.image-gallery img:hover {
    transform: scale(1.05);
}

/* Alert/Callout styling */
.alert {
    border-radius: 0.5rem;
    border: none;
    padding: 1.5rem;
    margin: 2rem 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

/* Table styling in preview */
#content-preview table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-radius: 0.5rem;
    overflow: hidden;
}

#content-preview th,
#content-preview td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

#content-preview th {
    background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
    color: white;
    font-weight: 600;
}

#content-preview tr:hover {
    background: #f8f9fa;
}

/* Responsive editor */
@media (max-width: 768px) {
    .content-editor-toolbar {
        padding: 0.5rem;
        gap: 0.25rem;
    }

    .editor-btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.75rem;
    }

    .btn-group {
        margin-bottom: 0.5rem;
    }
}

/* ===== IMAGE UPLOAD & EDITOR ===== */
.upload-progress {
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    animation: slideInDown 0.3s ease-out;
}

.image-preview-container {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    text-align: center;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.image-preview-container:hover {
    border-color: #ffc107;
    background: #fff9e6;
}

.image-preview-container img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.image-preview-container img:hover {
    transform: scale(1.02);
}

.image-controls {
    background: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    height: fit-content;
}

.image-controls h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #ffc107;
}

.image-controls .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.image-controls .form-control,
.image-controls .form-select {
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
    transition: all 0.2s ease;
}

.image-controls .form-control:focus,
.image-controls .form-select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.image-controls .form-range {
    accent-color: #ffc107;
}

.image-controls .form-check-input:checked {
    background-color: #ffc107;
    border-color: #ffc107;
}

/* Toast container */
.toast-container {
    z-index: 9999;
}

.toast {
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Image gallery in content */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
}

.image-gallery img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.image-gallery img:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Enhanced image styling in content */
.post-content-body img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

.post-content-body img.shadow {
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.post-content-body img:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

/* Drag and drop styling */
.drag-over {
    border-color: #ffc107 !important;
    background: #fff9e6 !important;
}

.drag-over::after {
    content: '📁 Thả file vào đây';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    color: #ffc107;
    font-weight: 600;
    pointer-events: none;
}

/* Loading animations */
@keyframes uploadPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.uploading {
    animation: uploadPulse 1.5s infinite;
}

/* Image editor modal enhancements */
.modal-lg .modal-content {
    border-radius: 1rem;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
    color: white;
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 1rem 2rem;
}

/* Code preview styling */
pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    font-size: 0.875rem;
    overflow-x: auto;
}

pre code {
    color: #495057;
    background: none;
    border: none;
    padding: 0;
}

/* Responsive image editor */
@media (max-width: 768px) {
    .modal-lg .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }

    .image-controls {
        margin-top: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--bg-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

/* Cards */
.card {
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: 1px solid var(--border-color);
}

.card-footer {
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    border-top: 1px solid var(--border-color);
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-warning {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-dark);
}

.btn-warning:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.btn-outline-warning {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-warning:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-dark);
}

/* Forms */
.form-control {
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.input-group-text {
    border: 1px solid var(--border-color);
    background-color: var(--bg-light);
    border-radius: 0.5rem 0 0 0.5rem;
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 0.5rem 0.5rem 0;
}

.input-group:focus-within .input-group-text {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
    color: var(--text-dark);
}

.card-title a {
    color: #2c3e50;
    transition: color 0.2s ease;
}

.card-title a:hover {
    color: #3498db;
}

.post-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2c3e50;
}

.post-content h1, .post-content h2, .post-content h3 {
    color: #34495e;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.post-content h2 {
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.post-content p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

.post-content blockquote {
    border-left: 4px solid #3498db;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
}

.btn-primary {
    background: linear-gradient(45deg, #3498db, #2980b9);
    border: none;
    border-radius: 0.5rem;
    padding: 0.5rem 1.5rem;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #2980b9, #1f5f8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-outline-primary {
    border-color: #3498db;
    color: #3498db;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.btn-outline-primary:hover {
    background-color: #3498db;
    border-color: #3498db;
    transform: translateY(-1px);
}

.alert {
    border: none;
    border-radius: 0.75rem;
    border-left: 4px solid;
}

.alert-success {
    border-left-color: #27ae60;
    background-color: #d5f4e6;
    color: #1e8449;
}

.alert-danger {
    border-left-color: #e74c3c;
    background-color: #fadbd8;
    color: #c0392b;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255,255,255,0.9);
    transition: color 0.2s ease;
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: white;
}

.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.badge {
    border-radius: 0.5rem;
}

.form-control {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.pagination .page-link {
    border-radius: 0.5rem;
    margin: 0 0.2rem;
    border: 1px solid #dee2e6;
    color: #3498db;
}

.pagination .page-item.active .page-link {
    background-color: #3498db;
    border-color: #3498db;
}

footer {
    margin-top: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
        margin: -1.5rem -15px 1rem -15px;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Animation for loading */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card {
    animation: fadeIn 0.5s ease-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}

/* Post Detail Styles */
.post-header-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 2rem 0;
    margin-top: -1rem;
}

.category-badge-large {
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
}

.featured-badge-large {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
}

.post-title-large {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-dark);
    font-family: 'Noto Serif', serif;
}

.author-avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.post-stats-large .stat-item {
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    margin: 0 0.25rem;
    font-size: 0.875rem;
    color: var(--text-muted);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.featured-image-container {
    animation: fadeInUp 0.8s ease-out;
}

.post-content-wrapper {
    background: white;
    border-radius: 1rem;
    padding: 3rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 3rem;
}

.post-content-body {
    font-size: 1.125rem;
    line-height: 1.8;
    color: #2c3e50;
}

.post-content-body h1,
.post-content-body h2,
.post-content-body h3,
.post-content-body h4,
.post-content-body h5,
.post-content-body h6 {
    font-family: 'Noto Serif', serif;
    color: var(--text-dark);
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.post-content-body h2 {
    font-size: 2rem;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.post-content-body h3 {
    font-size: 1.5rem;
    color: var(--accent-color);
}

.post-content-body p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

.post-content-body blockquote {
    border-left: 4px solid var(--primary-color);
    background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 0.5rem;
    font-style: italic;
    position: relative;
}

.post-content-body blockquote::before {
    content: '"';
    font-size: 4rem;
    color: var(--primary-color);
    position: absolute;
    top: -0.5rem;
    left: 1rem;
    opacity: 0.3;
}

.post-content-body ul,
.post-content-body ol {
    padding-left: 2rem;
    margin-bottom: 1.5rem;
}

.post-content-body li {
    margin-bottom: 0.5rem;
}

.post-tags .tag-item {
    background: var(--bg-light);
    color: var(--text-dark);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.875rem;
    margin: 0.25rem;
    display: inline-block;
    transition: all 0.3s ease;
}

.post-tags .tag-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.share-buttons .btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.share-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Responsive */
@media (max-width: 768px) {
    .post-title-large {
        font-size: 2rem;
    }

    .post-content-wrapper {
        padding: 2rem 1.5rem;
    }

    .post-content-body {
        font-size: 1rem;
    }

    .author-avatar-large {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

/* Reading Progress Bar */
.reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: rgba(0,0,0,0.1);
    z-index: 9999;
}

.reading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
}

/* Related Posts Section - Below Content */
.related-posts-section {
    animation: fadeInUp 0.6s ease-out;
    margin-top: 3rem;
}

.related-post-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.related-post-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.related-post-card:hover::before {
    transform: scaleX(1);
}

.related-post-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.related-post-image {
    border-radius: 0.75rem;
    overflow: hidden;
    transition: transform 0.4s ease;
}

.related-post-card:hover .related-post-image img {
    transform: scale(1.05);
}

.related-post-title {
    color: var(--text-dark);
    font-weight: 600;
    line-height: 1.4;
    transition: color 0.3s ease;
    font-size: 1rem;
}

.related-post-title a:hover {
    color: var(--primary-color) !important;
}

.related-post-excerpt {
    color: var(--text-muted);
    font-size: 0.875rem;
    line-height: 1.6;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-post-meta {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.related-post-content .badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
    border-radius: 50px;
    font-weight: 600;
}

.related-post-content .btn {
    border-radius: 50px;
    padding: 0.5rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.related-post-content .btn:hover {
    transform: translateX(3px);
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

/* Comment Section Improvements */
.comment-item {
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 0.5rem;
}

.comment-item:hover {
    background: #f8f9fa;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.comment-content {
    line-height: 1.6;
    color: var(--text-dark);
}

.comment-form {
    background: #f8f9fa;
    border-radius: 0.75rem;
    padding: 1.5rem;
}

/* Sidebar Styles */
.sidebar-sticky {
    position: sticky;
    top: 100px;
    z-index: 10;
}

/* Author Card Compact */
.author-card-compact .author-avatar-small {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.author-card .author-avatar-large {
    width: 80px;
    height: 80px;
    font-size: 2rem;
}

/* Related Posts Compact */
.related-item-compact {
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
}

.related-item-compact:last-child {
    border-bottom: none;
}

.related-item-compact:hover {
    background-color: #f8f9fa;
    transform: translateX(3px);
}

.related-link-compact {
    display: block;
    padding: 0.75rem;
    text-decoration: none;
    color: inherit;
}

.related-title-compact {
    font-size: 0.8rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.25rem;
    color: var(--text-dark);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-link-compact:hover .related-title-compact {
    color: var(--primary-color);
}

.related-meta-compact {
    font-size: 0.7rem;
    color: var(--text-muted);
}

/* Legacy support */
.related-posts-compact .related-item {
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.related-posts-compact .related-item:last-child {
    border-bottom: none;
}

.related-posts-compact .related-item:hover {
    background-color: #f8f9fa;
}

.related-posts-compact .related-link {
    display: block;
    padding: 1rem;
    text-decoration: none;
    color: inherit;
}

.related-posts-compact .related-title {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-posts-compact .related-link:hover .related-title {
    color: var(--primary-color);
}

.related-posts-compact .related-meta {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.quick-actions .btn {
    border-radius: 0.5rem;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.quick-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Comment improvements */
.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.comment-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 1rem;
    padding: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .sidebar-sticky {
        position: static;
        margin-top: 3rem;
    }
}

@media (max-width: 768px) {
    .related-post-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .related-posts-section .row {
        margin: 0;
    }

    .related-posts-section .col-md-6 {
        padding: 0.5rem;
    }

    .sidebar-sticky {
        margin-top: 2rem;
    }

    .related-posts-compact .related-title {
        font-size: 0.8rem;
    }

    .author-card .author-avatar-large {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}
