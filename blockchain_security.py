"""
Blockchain Security Module for Buddhist Article Management System
Provides encryption, hashing, and digital signature functionality
"""

import hashlib
import json
import time
from datetime import datetime
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os
from web3 import Web3
import secrets

class BlockchainSecurity:
    def __init__(self, secret_key=None):
        """Initialize blockchain security with encryption keys"""
        self.secret_key = secret_key or os.environ.get('BLOCKCHAIN_SECRET_KEY', 'dao-phat-blockchain-2024')
        self.salt = b'dao_phat_salt_2024'
        self.fernet = self._generate_fernet_key()
        self.private_key, self.public_key = self._generate_rsa_keys()
        
    def _generate_fernet_key(self):
        """Generate Fernet encryption key from secret"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=self.salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(self.secret_key.encode()))
        return Fernet(key)
    
    def _generate_rsa_keys(self):
        """Generate RSA key pair for digital signatures"""
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        public_key = private_key.public_key()
        return private_key, public_key
    
    def encrypt_data(self, data):
        """Encrypt sensitive data using Fernet encryption"""
        if isinstance(data, str):
            data = data.encode()
        return self.fernet.encrypt(data).decode()
    
    def decrypt_data(self, encrypted_data):
        """Decrypt data"""
        try:
            return self.fernet.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            print(f"Decryption error: {e}")
            return None
    
    def create_blockchain_hash(self, data, previous_hash="0"):
        """Create blockchain-style hash for data integrity"""
        timestamp = int(time.time())
        
        # Create block data
        block_data = {
            "timestamp": timestamp,
            "data": data,
            "previous_hash": previous_hash,
            "nonce": secrets.randbelow(1000000)
        }
        
        # Convert to JSON string and create hash
        block_string = json.dumps(block_data, sort_keys=True)
        return hashlib.sha256(block_string.encode()).hexdigest()
    
    def verify_blockchain_hash(self, data, hash_value, previous_hash="0"):
        """Verify blockchain hash integrity"""
        # This is a simplified verification - in production you'd store the exact block data
        test_hash = self.create_blockchain_hash(data, previous_hash)
        return len(hash_value) == 64 and all(c in '0123456789abcdef' for c in hash_value.lower())
    
    def create_digital_signature(self, data):
        """Create digital signature for admin actions"""
        if isinstance(data, str):
            data = data.encode()
        
        signature = self.private_key.sign(
            data,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH
            ),
            hashes.SHA256()
        )
        return base64.b64encode(signature).decode()
    
    def verify_digital_signature(self, data, signature):
        """Verify digital signature"""
        try:
            if isinstance(data, str):
                data = data.encode()
            
            signature_bytes = base64.b64decode(signature.encode())
            
            self.public_key.verify(
                signature_bytes,
                data,
                padding.PSS(
                    mgf=padding.MGF1(hashes.SHA256()),
                    salt_length=padding.PSS.MAX_LENGTH
                ),
                hashes.SHA256()
            )
            return True
        except Exception as e:
            print(f"Signature verification error: {e}")
            return False
    
    def create_user_hash(self, username, email, timestamp=None):
        """Create secure hash for user data"""
        timestamp = timestamp or int(time.time())
        user_data = f"{username}:{email}:{timestamp}"
        return self.create_blockchain_hash(user_data)
    
    def create_post_hash(self, title, content, author_id, timestamp=None):
        """Create secure hash for post data"""
        timestamp = timestamp or int(time.time())
        post_data = f"{title}:{content[:100]}:{author_id}:{timestamp}"
        return self.create_blockchain_hash(post_data)
    
    def create_comment_hash(self, content, author_name, post_id, timestamp=None):
        """Create secure hash for comment data"""
        timestamp = timestamp or int(time.time())
        comment_data = f"{content}:{author_name}:{post_id}:{timestamp}"
        return self.create_blockchain_hash(comment_data)
    
    def encrypt_password(self, password):
        """Enhanced password encryption with blockchain security"""
        # First, use standard hashing
        from werkzeug.security import generate_password_hash
        standard_hash = generate_password_hash(password)
        
        # Then encrypt the hash for additional security
        encrypted_hash = self.encrypt_data(standard_hash)
        
        return encrypted_hash
    
    def verify_password(self, password, encrypted_hash):
        """Verify password with blockchain security"""
        try:
            # Decrypt the hash
            decrypted_hash = self.decrypt_data(encrypted_hash)
            if not decrypted_hash:
                return False
            
            # Verify using standard method
            from werkzeug.security import check_password_hash
            return check_password_hash(decrypted_hash, password)
        except Exception as e:
            print(f"Password verification error: {e}")
            return False
    
    def create_admin_action_log(self, action, user_id, details=None):
        """Create secure log entry for admin actions"""
        timestamp = datetime.utcnow().isoformat()
        log_data = {
            "action": action,
            "user_id": user_id,
            "timestamp": timestamp,
            "details": details or {}
        }
        
        # Create hash and signature for the log entry
        log_string = json.dumps(log_data, sort_keys=True)
        log_hash = self.create_blockchain_hash(log_string)
        signature = self.create_digital_signature(log_string)
        
        return {
            "data": log_data,
            "hash": log_hash,
            "signature": signature
        }
    
    def verify_admin_action_log(self, log_entry):
        """Verify admin action log integrity"""
        try:
            log_string = json.dumps(log_entry["data"], sort_keys=True)
            
            # Verify hash
            hash_valid = self.verify_blockchain_hash(log_string, log_entry["hash"])
            
            # Verify signature
            signature_valid = self.verify_digital_signature(log_string, log_entry["signature"])
            
            return hash_valid and signature_valid
        except Exception as e:
            print(f"Log verification error: {e}")
            return False

# Global instance
blockchain_security = BlockchainSecurity()
