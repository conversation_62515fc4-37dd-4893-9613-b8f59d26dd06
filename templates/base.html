<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Nền Tảng <PERSON>a Sẻ Bài Viết <PERSON>{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif:wght@300;400;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    {% block head %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Top Bar -->
    <div class="top-bar bg-dark text-white py-2 d-none d-md-block">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small>
                        <i class="fas fa-quote-left me-2"></i>
                        "Hạnh phúc không phải là điều gì đó có sẵn. Nó đến từ hành động của chính bạn." - Đức Đạt Lai Lạt Ma
                    </small>
                </div>
                <div class="col-md-6 text-end">
                    <small>
                        <i class="fas fa-calendar me-1"></i>
                        {{ moment().format('DD/MM/YYYY') if moment else '' }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index') }}">
                <div class="logo-container me-3">
                    <i class="fas fa-dharmachakra text-warning"></i>
                </div>
                <div>
                    <h4 class="mb-0 text-dark fw-bold">Phật Học</h4>
                    <small class="text-muted">Nền tảng chia sẻ tri thức</small>
                </div>
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto ms-4">
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Trang chủ
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle fw-medium" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-book me-1"></i>Chủ đề
                        </a>
                        <ul class="dropdown-menu border-0 shadow">
                            {% for category in categories %}
                            <li><a class="dropdown-item" href="{{ url_for('category_posts', slug=category.slug) }}">
                                <i class="{{ category.icon }} me-2" style="color: {{ category.color }};"></i>
                                {{ category.name }}
                                <span class="badge bg-light text-dark ms-2">
                                    {{ category.posts|selectattr('is_published')|list|length }}
                                </span>
                            </a></li>
                            {% endfor %}
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-medium" href="#">
                            <i class="fas fa-users me-1"></i>Cộng đồng
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('create_post') }}">
                                <i class="fas fa-pen-fancy me-1"></i>Viết bài
                            </a>
                        </li>
                        {% if current_user.is_admin %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cogs me-1"></i>Quản lý
                                </a>
                                <ul class="dropdown-menu border-0 shadow">
                                    <li><a class="dropdown-item" href="{{ url_for('admin') }}">
                                        <i class="fas fa-dashboard me-2"></i>Dashboard
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('manage_users') }}">
                                        <i class="fas fa-users me-2"></i>Quản lý Users
                                    </a></li>
                                    <li><a class="dropdown-item" href="{{ url_for('manage_categories') }}">
                                        <i class="fas fa-tags me-2"></i>Quản lý Danh mục
                                    </a></li>
                                </ul>
                            </li>
                        {% endif %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user"></i>
                                </div>
                                <span class="d-none d-lg-inline">{{ current_user.full_name or current_user.username }}</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                                    <i class="fas fa-user-circle me-2"></i>Hồ sơ
                                </a></li>
                                <li><a class="dropdown-item" href="#">
                                    <i class="fas fa-cog me-2"></i>Cài đặt
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="{{ url_for('logout') }}">
                                    <i class="fas fa-sign-out-alt me-2"></i>Đăng xuất
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="btn btn-warning text-dark fw-medium px-4" href="{{ url_for('login') }}">
                                <i class="fas fa-sign-in-alt me-1"></i>Đăng nhập
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show border-0 shadow-sm" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-light mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-dharmachakra text-warning fa-2x me-3"></i>
                        <div>
                            <h5 class="mb-0">Phật Học</h5>
                            <small class="text-muted">Nền tảng chia sẻ tri thức</small>
                        </div>
                    </div>
                    <p class="text-muted">
                        Nơi chia sẻ những bài viết sâu sắc về triết lý Phật giáo,
                        giúp mọi người hiểu rõ hơn về con đường giác ngộ và hạnh phúc.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-youtube fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-telegram fa-lg"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-warning mb-3">Chủ đề</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Từ bi</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Trí tuệ</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Thiền định</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Tu tập</a></li>
                    </ul>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-warning mb-3">Liên kết</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-muted text-decoration-none">Về chúng tôi</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Liên hệ</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Điều khoản</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">Bảo mật</a></li>
                    </ul>
                </div>

                <div class="col-lg-4 mb-4">
                    <h6 class="text-warning mb-3">Đăng ký nhận bài viết mới</h6>
                    <p class="text-muted small">Nhận thông báo khi có bài viết mới được đăng tải.</p>
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Email của bạn">
                        <button class="btn btn-warning text-dark" type="button">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>

            <hr class="my-4 border-secondary">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted small">
                        &copy; 2024 Phật Học Platform. Tất cả quyền được bảo lưu.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted small">
                        Được phát triển với <i class="fas fa-heart text-danger"></i> cho cộng đồng Phật tử
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Action Buttons -->
    <div class="floating-buttons">
        <a href="{{ url_for('index') }}" class="floating-btn home-btn" title="Trang chủ">
            <i class="fas fa-home"></i>
        </a>
        <button class="floating-btn back-to-top" title="Lên đầu trang" onclick="scrollToTop()">
            <i class="fas fa-arrow-up"></i>
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    <!-- Floating Buttons Script -->
    <script>
        // Show/hide floating buttons based on scroll
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const floatingButtons = document.querySelectorAll('.floating-btn');

            if (scrollTop > 300) {
                floatingButtons.forEach(btn => btn.classList.add('show'));
            } else {
                floatingButtons.forEach(btn => btn.classList.remove('show'));
            }
        });

        // Smooth scroll to top
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Animate elements on scroll
        function animateOnScroll() {
            const elements = document.querySelectorAll('.animate-on-scroll');
            const windowHeight = window.innerHeight;

            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < windowHeight - elementVisible) {
                    element.classList.add('animated');

                    // Add specific animation class based on data attribute
                    const animation = element.getAttribute('data-animation') || 'fadeInUp';
                    const delay = element.getAttribute('data-delay') || 0;

                    setTimeout(() => {
                        element.classList.add(animation);
                    }, delay);
                }
            });
        }

        // Animate post cards on scroll
        function animatePostCards() {
            const postCards = document.querySelectorAll('.post-card-modern:not(.animate-in)');
            const windowHeight = window.innerHeight;

            postCards.forEach((card, index) => {
                const cardTop = card.getBoundingClientRect().top;

                if (cardTop < windowHeight - 100) {
                    setTimeout(() => {
                        card.classList.add('animate-in');
                    }, index * 100);
                }
            });
        }

        // Initialize animations
        window.addEventListener('scroll', function() {
            animateOnScroll();
            animatePostCards();
        });

        // Run animations on page load
        document.addEventListener('DOMContentLoaded', function() {
            animateOnScroll();
            animatePostCards();
        });
    </script>
</body>
</html>
