{% extends "base.html" %}

{% block title %}{% if post %}Sửa bài viết{% else %}Tạo bài viết mới{% endif %} - Đ<PERSON><PERSON>{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4>
                    <i class="fas fa-{% if post %}edit{% else %}plus{% endif %}"></i>
                    {% if post %}Sửa bài viết{% else %}Tạo bài viết mới{% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control") }}
                        {% if form.title.errors %}
                            <div class="text-danger small">
                                {% for error in form.title.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.summary.label(class="form-label") }}
                            {{ form.summary(class="form-control") }}
                            <div class="form-text">Tóm tắt ngắn gọn về nội dung bài viết</div>
                            {% if form.summary.errors %}
                                <div class="text-danger small">
                                    {% for error in form.summary.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.category_id.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-tags"></i>
                                </span>
                                {{ form.category_id(class="form-control") }}
                            </div>
                            <div class="form-text">Chọn chủ đề phù hợp cho bài viết</div>
                            {% if form.category_id.errors %}
                                <div class="text-danger small">
                                    {% for error in form.category_id.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Featured Image Upload -->
                    <div class="mb-3">
                        {{ form.featured_image.label(class="form-label") }}
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-image"></i>
                            </span>
                            {{ form.featured_image(class="form-control") }}
                        </div>
                        <div class="form-text">Chọn hình ảnh đại diện cho bài viết (JPG, PNG, GIF)</div>
                        {% if form.featured_image.errors %}
                            <div class="text-danger small">
                                {% for error in form.featured_image.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}

                        <!-- Enhanced Content Editor Toolbar -->
                        <div class="content-editor-toolbar">
                            <div class="btn-group btn-group-sm me-2">
                                <button type="button" class="editor-btn" onclick="formatText('**', '**')" title="In đậm">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('*', '*')" title="In nghiêng">
                                    <i class="fas fa-italic"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('~~', '~~')" title="Gạch ngang">
                                    <i class="fas fa-strikethrough"></i>
                                </button>
                            </div>

                            <div class="btn-group btn-group-sm me-2">
                                <button type="button" class="editor-btn" onclick="formatText('\\n## ', '')" title="Tiêu đề H2">
                                    <i class="fas fa-heading"></i> H2
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n### ', '')" title="Tiêu đề H3">
                                    <i class="fas fa-heading"></i> H3
                                </button>
                            </div>

                            <div class="btn-group btn-group-sm me-2">
                                <button type="button" class="editor-btn" onclick="formatText('\\n- ', '')" title="Danh sách">
                                    <i class="fas fa-list-ul"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n1. ', '')" title="Danh sách số">
                                    <i class="fas fa-list-ol"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n> ', '')" title="Trích dẫn">
                                    <i class="fas fa-quote-right"></i>
                                </button>
                            </div>

                            <div class="btn-group btn-group-sm me-2">
                                <button type="button" class="editor-btn" onclick="insertLink()" title="Chèn link">
                                    <i class="fas fa-link"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertImage()" title="Chèn hình ảnh">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertYouTube()" title="Chèn video YouTube">
                                    <i class="fab fa-youtube"></i>
                                </button>
                            </div>

                            <div class="btn-group btn-group-sm">
                                <button type="button" class="editor-btn" onclick="insertAnchor()" title="Chèn neo (anchor)">
                                    <i class="fas fa-anchor"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('`', '`')" title="Code inline">
                                    <i class="fas fa-code"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n```\\n', '\\n```\\n')" title="Code block">
                                    <i class="fas fa-file-code"></i>
                                </button>
                            </div>
                        </div>

                        {{ form.content(class="form-control", rows="15", id="content-editor", style="border-radius: 0 0 0.375rem 0.375rem; border-top: none;") }}

                        <!-- Preview Area (hidden by default) -->
                        <div id="content-preview" class="form-control" style="display: none; min-height: 400px; border-radius: 0 0 0.375rem 0.375rem; border-top: none; background: #f8f9fa;"></div>

                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div class="form-text">
                                Nội dung chi tiết của bài viết (hỗ trợ Markdown).
                                <strong>Mẹo:</strong> Sử dụng toolbar để format text nhanh chóng.
                                <br><strong>Phím tắt:</strong> Ctrl+B (đậm), Ctrl+I (nghiêng), Ctrl+K (link)
                            </div>
                            <div class="text-end">
                                <small class="text-muted" id="word-count">0 từ, 0 ký tự</small>
                                <br>
                                <button type="button" class="btn btn-sm btn-outline-info" onclick="togglePreview()">
                                    <i class="fas fa-eye"></i> Preview
                                </button>
                            </div>
                        </div>
                        {% if form.content.errors %}
                            <div class="text-danger small">
                                {% for error in form.content.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- SEO Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-search me-2"></i>Cài đặt SEO
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.meta_title.label(class="form-label") }}
                                    {{ form.meta_title(class="form-control") }}
                                    <div class="form-text">Tiêu đề SEO (tối đa 60 ký tự)</div>
                                    {% if form.meta_title.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.meta_title.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-3">
                                    {{ form.keywords.label(class="form-label") }}
                                    {{ form.keywords(class="form-control") }}
                                    <div class="form-text">Từ khóa SEO (phân cách bằng dấu phẩy)</div>
                                    {% if form.keywords.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.keywords.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                {{ form.meta_description.label(class="form-label") }}
                                {{ form.meta_description(class="form-control", rows="3") }}
                                <div class="form-text">Mô tả SEO (tối đa 160 ký tự)</div>
                                {% if form.meta_description.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.meta_description.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Publishing Options -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-2"></i>Tùy chọn xuất bản
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        {{ form.is_published(class="form-check-input") }}
                                        {{ form.is_published.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        {{ form.is_featured(class="form-check-input") }}
                                        {{ form.is_featured.label(class="form-check-label") }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Enhanced content editor with advanced features
document.addEventListener('DOMContentLoaded', function() {
    const contentEditor = document.getElementById('content-editor');

    // Auto-resize textarea
    contentEditor.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Add keyboard shortcuts
    contentEditor.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'b':
                    e.preventDefault();
                    formatText('**', '**');
                    break;
                case 'i':
                    e.preventDefault();
                    formatText('*', '*');
                    break;
                case 'k':
                    e.preventDefault();
                    insertLink();
                    break;
            }
        }
    });
});

function formatText(before, after) {
    const textarea = document.getElementById('content-editor');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);

    const newText = before + selectedText + after;
    textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);

    // Set cursor position
    const newCursorPos = start + before.length + selectedText.length + after.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();
}

function insertLink() {
    const url = prompt('Nhập URL:');
    if (url) {
        const text = prompt('Nhập text hiển thị:', url);
        if (text) {
            formatText(`[${text}](`, `${url})`);
        }
    }
}

function insertImage() {
    const url = prompt('Nhập URL hình ảnh:');
    if (url) {
        const alt = prompt('Nhập mô tả hình ảnh:', 'Hình ảnh');
        if (alt) {
            formatText(`![${alt}](`, `${url})`);
        }
    }
}

function insertYouTube() {
    const url = prompt('Nhập URL YouTube:');
    if (url) {
        let videoId = '';

        // Extract video ID from various YouTube URL formats
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);

        if (match && match[2].length === 11) {
            videoId = match[2];
        } else {
            alert('URL YouTube không hợp lệ!');
            return;
        }

        const embedCode = `\\n\\n<div class="video-container">\\n<iframe width="560" height="315" src="https://www.youtube.com/embed/${videoId}" frameborder="0" allowfullscreen></iframe>\\n</div>\\n\\n`;
        formatText(embedCode, '');
    }
}

function insertAnchor() {
    const anchorId = prompt('Nhập ID cho neo (anchor):');
    if (anchorId) {
        const text = prompt('Nhập text hiển thị:', anchorId);
        if (text) {
            formatText(`<a id="${anchorId}"></a>\\n\\n## ${text}`, '');
        }
    }
}

// Preview functionality
function togglePreview() {
    const textarea = document.getElementById('content-editor');
    const previewDiv = document.getElementById('content-preview');

    if (previewDiv.style.display === 'none') {
        // Show preview
        previewDiv.innerHTML = marked(textarea.value);
        previewDiv.style.display = 'block';
        textarea.style.display = 'none';
    } else {
        // Show editor
        previewDiv.style.display = 'none';
        textarea.style.display = 'block';
    }
}

// Word count
function updateWordCount() {
    const textarea = document.getElementById('content-editor');
    const wordCount = textarea.value.split(/\s+/).filter(word => word.length > 0).length;
    const charCount = textarea.value.length;

    document.getElementById('word-count').textContent = `${wordCount} từ, ${charCount} ký tự`;
}

// Update word count on input
document.getElementById('content-editor').addEventListener('input', updateWordCount);

// Initial word count
updateWordCount();
</script>
{% endblock %}
