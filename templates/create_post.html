{% extends "base.html" %}

{% block title %}{% if post %}Sửa bài viết{% else %}Tạo bài viết mới{% endif %} - Đ<PERSON><PERSON>{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4>
                    <i class="fas fa-{% if post %}edit{% else %}plus{% endif %}"></i>
                    {% if post %}Sửa bài viết{% else %}Tạo bài viết mới{% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control") }}
                        {% if form.title.errors %}
                            <div class="text-danger small">
                                {% for error in form.title.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.summary.label(class="form-label") }}
                            {{ form.summary(class="form-control") }}
                            <div class="form-text">Tóm tắt ngắn gọn về nội dung bài viết</div>
                            {% if form.summary.errors %}
                                <div class="text-danger small">
                                    {% for error in form.summary.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.category_id.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-tags"></i>
                                </span>
                                {{ form.category_id(class="form-control") }}
                            </div>
                            <div class="form-text">Chọn chủ đề phù hợp cho bài viết</div>
                            {% if form.category_id.errors %}
                                <div class="text-danger small">
                                    {% for error in form.category_id.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Featured Image Upload -->
                    <div class="mb-3">
                        {{ form.featured_image.label(class="form-label") }}
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-image"></i>
                            </span>
                            {{ form.featured_image(class="form-control") }}
                        </div>
                        <div class="form-text">Chọn hình ảnh đại diện cho bài viết (JPG, PNG, GIF)</div>
                        {% if form.featured_image.errors %}
                            <div class="text-danger small">
                                {% for error in form.featured_image.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}

                        <!-- Enhanced Content Editor Toolbar -->
                        <div class="content-editor-toolbar">
                            <!-- Text Formatting -->
                            <div class="btn-group btn-group-sm me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Định dạng văn bản">
                                <button type="button" class="editor-btn" onclick="formatText('**', '**')"
                                        data-bs-toggle="tooltip" title="In đậm (Ctrl+B)">
                                    <i class="fas fa-bold"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('*', '*')"
                                        data-bs-toggle="tooltip" title="In nghiêng (Ctrl+I)">
                                    <i class="fas fa-italic"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('~~', '~~')"
                                        data-bs-toggle="tooltip" title="Gạch ngang văn bản">
                                    <i class="fas fa-strikethrough"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('<u>', '</u>')"
                                        data-bs-toggle="tooltip" title="Gạch chân văn bản">
                                    <i class="fas fa-underline"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('<mark>', '</mark>')"
                                        data-bs-toggle="tooltip" title="Highlight văn bản">
                                    <i class="fas fa-highlighter"></i>
                                </button>
                            </div>

                            <!-- Headers -->
                            <div class="btn-group btn-group-sm me-2" data-bs-toggle="tooltip" title="Tiêu đề">
                                <button type="button" class="editor-btn" onclick="formatText('\\n# ', '')"
                                        data-bs-toggle="tooltip" title="Tiêu đề chính (H1)">
                                    <i class="fas fa-heading"></i> H1
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n## ', '')"
                                        data-bs-toggle="tooltip" title="Tiêu đề phụ (H2)">
                                    <i class="fas fa-heading"></i> H2
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n### ', '')"
                                        data-bs-toggle="tooltip" title="Tiêu đề nhỏ (H3)">
                                    <i class="fas fa-heading"></i> H3
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n#### ', '')"
                                        data-bs-toggle="tooltip" title="Tiêu đề rất nhỏ (H4)">
                                    <i class="fas fa-heading"></i> H4
                                </button>
                            </div>

                            <!-- Lists & Quotes -->
                            <div class="btn-group btn-group-sm me-2" data-bs-toggle="tooltip" title="Danh sách và trích dẫn">
                                <button type="button" class="editor-btn" onclick="formatText('\\n- ', '')"
                                        data-bs-toggle="tooltip" title="Danh sách dấu chấm">
                                    <i class="fas fa-list-ul"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n1. ', '')"
                                        data-bs-toggle="tooltip" title="Danh sách đánh số">
                                    <i class="fas fa-list-ol"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertCheckList()"
                                        data-bs-toggle="tooltip" title="Danh sách checkbox">
                                    <i class="fas fa-tasks"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="formatText('\\n> ', '')"
                                        data-bs-toggle="tooltip" title="Trích dẫn">
                                    <i class="fas fa-quote-right"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertCallout()"
                                        data-bs-toggle="tooltip" title="Hộp thông tin nổi bật">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </button>
                            </div>

                            <!-- Media -->
                            <div class="btn-group btn-group-sm me-2" data-bs-toggle="tooltip" title="Media và liên kết">
                                <button type="button" class="editor-btn" onclick="insertLink()"
                                        data-bs-toggle="tooltip" title="Chèn liên kết (Ctrl+K)">
                                    <i class="fas fa-link"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertImage()"
                                        data-bs-toggle="tooltip" title="Chèn hình ảnh từ URL">
                                    <i class="fas fa-image"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="uploadImage()" id="upload-btn"
                                        data-bs-toggle="tooltip" title="Upload hình ảnh từ máy tính (Cần đăng nhập)">
                                    <i class="fas fa-upload"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertGallery()"
                                        data-bs-toggle="tooltip" title="Tạo gallery hình ảnh">
                                    <i class="fas fa-images"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertYouTube()"
                                        data-bs-toggle="tooltip" title="Chèn video YouTube">
                                    <i class="fab fa-youtube"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertAudio()"
                                        data-bs-toggle="tooltip" title="Chèn file âm thanh">
                                    <i class="fas fa-music"></i>
                                </button>
                            </div>

                            <!-- Advanced -->
                            <div class="btn-group btn-group-sm me-2" data-bs-toggle="tooltip" title="Tính năng nâng cao">
                                <button type="button" class="editor-btn" onclick="insertTable()"
                                        data-bs-toggle="tooltip" title="Chèn bảng">
                                    <i class="fas fa-table"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertAnchor()"
                                        data-bs-toggle="tooltip" title="Chèn neo (anchor) để liên kết nội bộ">
                                    <i class="fas fa-anchor"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertTOC()"
                                        data-bs-toggle="tooltip" title="Tạo mục lục tự động">
                                    <i class="fas fa-list-alt"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertSeparator()"
                                        data-bs-toggle="tooltip" title="Chèn đường phân cách">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>

                            <!-- Code -->
                            <div class="btn-group btn-group-sm me-2" data-bs-toggle="tooltip" title="Code và kỹ thuật">
                                <button type="button" class="editor-btn" onclick="formatText('`', '`')"
                                        data-bs-toggle="tooltip" title="Code inline">
                                    <i class="fas fa-code"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertCodeBlock()"
                                        data-bs-toggle="tooltip" title="Code block với syntax highlighting">
                                    <i class="fas fa-file-code"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertMath()"
                                        data-bs-toggle="tooltip" title="Công thức toán học (LaTeX)">
                                    <i class="fas fa-square-root-alt"></i>
                                </button>
                            </div>

                            <!-- Tools -->
                            <div class="btn-group btn-group-sm" data-bs-toggle="tooltip" title="Công cụ">
                                <button type="button" class="editor-btn" onclick="insertEmoji()"
                                        data-bs-toggle="tooltip" title="Chèn emoji">
                                    <i class="fas fa-smile"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="insertDate()"
                                        data-bs-toggle="tooltip" title="Chèn ngày tháng hiện tại">
                                    <i class="fas fa-calendar"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="clearFormatting()"
                                        data-bs-toggle="tooltip" title="Xóa định dạng">
                                    <i class="fas fa-eraser"></i>
                                </button>
                                <button type="button" class="editor-btn" onclick="toggleFullscreen()"
                                        data-bs-toggle="tooltip" title="Chế độ toàn màn hình">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>

                        {{ form.content(class="form-control", rows="15", id="content-editor", style="border-radius: 0 0 0.375rem 0.375rem; border-top: none;") }}

                        <!-- Preview Area (hidden by default) -->
                        <div id="content-preview" class="form-control" style="display: none; min-height: 400px; border-radius: 0 0 0.375rem 0.375rem; border-top: none; background: #f8f9fa;"></div>

                        <!-- Hidden file input for image upload -->
                        <input type="file" id="image-upload-input" accept="image/*" style="display: none;" multiple>

                        <div class="d-flex justify-content-between align-items-center mt-2">
                            <div class="form-text">
                                <strong>📝 Editor nâng cao:</strong> Hỗ trợ Markdown, HTML, và nhiều tính năng khác.
                                <br><strong>⌨️ Phím tắt:</strong>
                                <span class="badge bg-light text-dark me-1">Ctrl+B</span> đậm,
                                <span class="badge bg-light text-dark me-1">Ctrl+I</span> nghiêng,
                                <span class="badge bg-light text-dark me-1">Ctrl+K</span> link,
                                <span class="badge bg-light text-dark me-1">Ctrl+P</span> preview,
                                <span class="badge bg-light text-dark me-1">Ctrl+Enter</span> fullscreen
                                <br><strong>💡 Mẹo:</strong> Rê chuột vào các nút để xem hướng dẫn chi tiết
                            </div>
                            <div class="text-end">
                                <small class="text-muted" id="word-count">0 từ, 0 ký tự</small>
                                <br>
                                <div class="btn-group btn-group-sm mt-1">
                                    <button type="button" class="btn btn-outline-info" onclick="togglePreview()" id="preview-btn"
                                            data-bs-toggle="tooltip" title="Xem trước bài viết (Ctrl+P)">
                                        <i class="fas fa-eye"></i> Preview
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="toggleSideBySide()" id="side-by-side-btn"
                                            data-bs-toggle="tooltip" title="Hiển thị editor và preview cạnh nhau">
                                        <i class="fas fa-columns"></i> Side by Side
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="showHelp()"
                                            data-bs-toggle="tooltip" title="Hướng dẫn sử dụng editor">
                                        <i class="fas fa-question-circle"></i> Help
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% if form.content.errors %}
                            <div class="text-danger small">
                                {% for error in form.content.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- SEO Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-search me-2"></i>Cài đặt SEO
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.meta_title.label(class="form-label") }}
                                    {{ form.meta_title(class="form-control") }}
                                    <div class="form-text">Tiêu đề SEO (tối đa 60 ký tự)</div>
                                    {% if form.meta_title.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.meta_title.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-3">
                                    {{ form.keywords.label(class="form-label") }}
                                    {{ form.keywords(class="form-control") }}
                                    <div class="form-text">Từ khóa SEO (phân cách bằng dấu phẩy)</div>
                                    {% if form.keywords.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.keywords.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                {{ form.meta_description.label(class="form-label") }}
                                {{ form.meta_description(class="form-control", rows="3") }}
                                <div class="form-text">Mô tả SEO (tối đa 160 ký tự)</div>
                                {% if form.meta_description.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.meta_description.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Publishing Options -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-2"></i>Tùy chọn xuất bản
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        {{ form.is_published(class="form-check-input") }}
                                        {{ form.is_published.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        {{ form.is_featured(class="form-check-input") }}
                                        {{ form.is_featured.label(class="form-check-label") }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
// Enhanced content editor with advanced features
let isPreviewMode = false;
let isSideBySideMode = false;
let isFullscreenMode = false;

// Initialize tooltips function
function initializeTooltips() {
    // Dispose existing tooltips first
    const existingTooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    existingTooltips.forEach(element => {
        const tooltip = bootstrap.Tooltip.getInstance(element);
        if (tooltip) {
            tooltip.dispose();
        }
    });

    // Initialize new tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
        new bootstrap.Tooltip(tooltipTriggerEl, {
            placement: 'top',
            trigger: 'hover focus',
            delay: { show: 500, hide: 100 }
        });
    });
}

document.addEventListener('DOMContentLoaded', function() {
    const contentEditor = document.getElementById('content-editor');

    // Initialize tooltips
    initializeTooltips();

    // Auto-resize textarea
    contentEditor.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
        updateWordCount();
        if (isSideBySideMode) {
            updatePreview();
        }
    });

    // Add keyboard shortcuts
    contentEditor.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'b':
                    e.preventDefault();
                    formatText('**', '**');
                    break;
                case 'i':
                    e.preventDefault();
                    formatText('*', '*');
                    break;
                case 'k':
                    e.preventDefault();
                    insertLink();
                    break;
                case 'Enter':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'p':
                    e.preventDefault();
                    togglePreview();
                    break;
            }
        }

        // Tab for indentation
        if (e.key === 'Tab') {
            e.preventDefault();
            formatText('    ', '');
        }
    });

    // Initial setup
    updateWordCount();

    // Setup drag and drop
    setupDragAndDrop();
});

// ===== DRAG AND DROP FUNCTIONALITY =====
function setupDragAndDrop() {
    const textarea = document.getElementById('content-editor');

    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        textarea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop area
    ['dragenter', 'dragover'].forEach(eventName => {
        textarea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        textarea.addEventListener(eventName, unhighlight, false);
    });

    // Handle dropped files
    textarea.addEventListener('drop', handleDrop, false);
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    const textarea = document.getElementById('content-editor');
    textarea.classList.add('drag-over');
}

function unhighlight(e) {
    const textarea = document.getElementById('content-editor');
    textarea.classList.remove('drag-over');
}

async function handleDrop(e) {
    // Check login status first
    const isLoggedIn = await checkLoginStatus();
    if (!isLoggedIn) {
        return;
    }

    const dt = e.dataTransfer;
    const files = dt.files;

    if (files.length > 0) {
        for (let i = 0; i < files.length; i++) {
            if (files[i].type.startsWith('image/')) {
                uploadSingleImage(files[i]);
            }
        }
    }
}

function formatText(before, after) {
    const textarea = document.getElementById('content-editor');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);

    const newText = before + selectedText + after;
    textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);

    // Set cursor position
    const newCursorPos = start + before.length + selectedText.length + after.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();

    // Update preview if in side-by-side mode
    if (isSideBySideMode) {
        updatePreview();
    }
}

// ===== BASIC FUNCTIONS =====
function insertLink() {
    const url = prompt('Nhập URL:');
    if (url) {
        const text = prompt('Nhập text hiển thị:', url);
        if (text) {
            formatText(`[${text}](`, `${url})`);
        }
    }
}

function insertImage() {
    const url = prompt('Nhập URL hình ảnh:');
    if (url) {
        const alt = prompt('Nhập mô tả hình ảnh:', 'Hình ảnh');
        const width = prompt('Nhập chiều rộng (tùy chọn, ví dụ: 500px):', '');
        if (alt) {
            if (width) {
                formatText(`<img src="${url}" alt="${alt}" style="width: ${width}; max-width: 100%;" class="img-fluid rounded">`, '');
            } else {
                formatText(`![${alt}](`, `${url})`);
            }
        }
    }
}

// ===== IMAGE UPLOAD FUNCTIONS =====
async function uploadImage() {
    // Check if user is logged in first
    const isLoggedIn = await checkLoginStatus();
    if (!isLoggedIn) {
        return;
    }

    const fileInput = document.getElementById('image-upload-input');
    fileInput.click();
}

async function checkLoginStatus() {
    try {
        const response = await fetch('/check_login');
        const data = await response.json();

        if (!data.logged_in) {
            alert('Bạn cần đăng nhập để sử dụng tính năng này. Vui lòng đăng nhập lại.');
            window.location.href = '/login';
            return false;
        }

        return true;
    } catch (error) {
        console.error('Error checking login status:', error);
        // Fallback to simple check
        const userInfo = document.querySelector('.navbar .dropdown-toggle');
        if (!userInfo || userInfo.textContent.includes('Đăng nhập')) {
            alert('Bạn cần đăng nhập để sử dụng tính năng này. Vui lòng đăng nhập lại.');
            window.location.href = '/login';
            return false;
        }
        return true;
    }
}

// Handle file selection
document.getElementById('image-upload-input').addEventListener('change', function(e) {
    const files = e.target.files;
    if (files.length > 0) {
        for (let i = 0; i < files.length; i++) {
            uploadSingleImage(files[i]);
        }
    }
});

function uploadSingleImage(file) {
    console.log('Starting upload for file:', file.name, 'Size:', file.size, 'Type:', file.type);

    // Validate file type
    if (!file.type.startsWith('image/')) {
        alert('Vui lòng chọn file hình ảnh!');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        alert('File quá lớn! Vui lòng chọn file nhỏ hơn 5MB.');
        return;
    }

    // Show upload progress
    const progressId = 'upload-progress-' + Date.now();
    showUploadProgress(progressId, file.name);

    // Create FormData
    const formData = new FormData();
    formData.append('image', file);

    console.log('FormData created, uploading...');

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
        formData.append('csrf_token', csrfToken);
    }

    // Upload file
    fetch('/upload_image', {
        method: 'POST',
        body: formData
        // Don't set Content-Type header, let browser set it with boundary for multipart/form-data
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        // Handle authentication errors
        if (response.status === 401 || response.status === 403) {
            throw new Error('Bạn cần đăng nhập để upload ảnh. Vui lòng đăng nhập lại.');
        }

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            if (response.status === 200) {
                throw new Error('Server returned HTML instead of JSON. You may need to log in again.');
            }
            throw new Error(`Server error: ${response.status}. Check if you are logged in.`);
        }

        return response.json();
    })
    .then(data => {
        hideUploadProgress(progressId);
        console.log('Upload response:', data);

        if (data.success) {
            // Show image editor modal
            showImageEditor(data);
        } else {
            alert('Upload thất bại: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        hideUploadProgress(progressId);
        console.error('Upload error:', error);

        // Handle authentication errors
        if (error.message.includes('đăng nhập')) {
            if (confirm('Bạn cần đăng nhập lại. Chuyển đến trang đăng nhập?')) {
                window.location.href = '/login';
            }
        } else {
            alert('Lỗi upload: ' + error.message + '. Vui lòng kiểm tra console để biết thêm chi tiết.');
        }
    });
}

function showUploadProgress(progressId, filename) {
    const progressHtml = `
        <div id="${progressId}" class="upload-progress alert alert-info">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                <span>Đang upload: ${filename}</span>
            </div>
        </div>
    `;

    const textarea = document.getElementById('content-editor');
    textarea.insertAdjacentHTML('beforebegin', progressHtml);
}

function hideUploadProgress(progressId) {
    const progressElement = document.getElementById(progressId);
    if (progressElement) {
        progressElement.remove();
    }
}

function showImageEditor(imageData) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit"></i> Chỉnh sửa hình ảnh
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="image-preview-container">
                                <img src="${imageData.url}" class="img-fluid rounded" id="preview-image" alt="Preview">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="image-controls">
                                <h6><i class="fas fa-cog"></i> Tùy chọn</h6>

                                <div class="mb-3">
                                    <label class="form-label">Mô tả (Alt text):</label>
                                    <input type="text" class="form-control" id="image-alt" value="Hình ảnh" placeholder="Mô tả hình ảnh">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Chiều rộng:</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="image-width" value="${imageData.width}" min="50" max="2000">
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Chiều cao:</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="image-height" value="${imageData.height}" min="50" max="2000">
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Chất lượng:</label>
                                    <input type="range" class="form-range" id="image-quality" min="10" max="100" value="85">
                                    <small class="text-muted">Chất lượng: <span id="quality-value">85</span>%</small>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Căn chỉnh:</label>
                                    <select class="form-select" id="image-align">
                                        <option value="none">Không căn chỉnh</option>
                                        <option value="left">Căn trái</option>
                                        <option value="center">Căn giữa</option>
                                        <option value="right">Căn phải</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="image-responsive" checked>
                                        <label class="form-check-label" for="image-responsive">
                                            Responsive (tự động điều chỉnh)
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="image-rounded">
                                        <label class="form-check-label" for="image-rounded">
                                            Bo góc
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="image-shadow">
                                        <label class="form-check-label" for="image-shadow">
                                            Đổ bóng
                                        </label>
                                    </div>
                                </div>

                                <button class="btn btn-warning btn-sm w-100 mb-2" onclick="resizeImage('${imageData.filename}')">
                                    <i class="fas fa-compress-alt"></i> Resize ảnh
                                </button>

                                <button class="btn btn-info btn-sm w-100" onclick="previewImageCode()">
                                    <i class="fas fa-eye"></i> Xem code
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="alert alert-info">
                            <small>
                                <strong>Thông tin file:</strong><br>
                                Tên: ${imageData.filename}<br>
                                Kích thước: ${imageData.width} x ${imageData.height}px<br>
                                URL: <code>${imageData.url}</code>
                            </small>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-primary" onclick="insertImageCode('${imageData.filename}', '${imageData.url}')">
                        <i class="fas fa-plus"></i> Chèn vào bài viết
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Re-initialize tooltips for modal content
    setTimeout(() => {
        initializeTooltips();
    }, 100);

    // Update quality value display
    document.getElementById('image-quality').addEventListener('input', function() {
        document.getElementById('quality-value').textContent = this.value;
    });

    // Remove modal after hiding
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function insertYouTube() {
    const url = prompt('Nhập URL YouTube:');
    if (url) {
        let videoId = '';

        // Extract video ID from various YouTube URL formats
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);

        if (match && match[2].length === 11) {
            videoId = match[2];
        } else {
            alert('URL YouTube không hợp lệ!');
            return;
        }

        const embedCode = `\\n\\n<div class="video-container">\\n<iframe width="560" height="315" src="https://www.youtube.com/embed/${videoId}" frameborder="0" allowfullscreen></iframe>\\n</div>\\n\\n`;
        formatText(embedCode, '');
    }
}

function insertAnchor() {
    const anchorId = prompt('Nhập ID cho neo (anchor):');
    if (anchorId) {
        const text = prompt('Nhập text hiển thị:', anchorId);
        if (text) {
            formatText(`<a id="${anchorId}"></a>\\n\\n## ${text}`, '');
        }
    }
}

// ===== NEW ADVANCED FUNCTIONS =====
function insertCheckList() {
    const items = prompt('Nhập các mục (phân cách bằng dấu phẩy):');
    if (items) {
        const checkList = items.split(',').map(item => `\\n- [ ] ${item.trim()}`).join('');
        formatText(checkList, '');
    }
}

function insertCallout() {
    const type = prompt('Chọn loại callout (info, warning, success, danger):', 'info');
    const content = prompt('Nhập nội dung:');
    if (content) {
        const callout = `\\n\\n<div class="alert alert-${type}" role="alert">\\n<i class="fas fa-info-circle"></i> ${content}\\n</div>\\n\\n`;
        formatText(callout, '');
    }
}

function insertGallery() {
    const urls = prompt('Nhập URLs hình ảnh (phân cách bằng dấu phẩy):');
    if (urls) {
        const images = urls.split(',').map(url => `<img src="${url.trim()}" class="gallery-img" alt="Gallery image">`).join('\\n');
        const gallery = `\\n\\n<div class="image-gallery">\\n${images}\\n</div>\\n\\n`;
        formatText(gallery, '');
    }
}

function insertAudio() {
    const url = prompt('Nhập URL file âm thanh:');
    if (url) {
        const audio = `\\n\\n<audio controls>\\n<source src="${url}" type="audio/mpeg">\\nTrình duyệt của bạn không hỗ trợ audio.\\n</audio>\\n\\n`;
        formatText(audio, '');
    }
}

function insertTable() {
    const rows = parseInt(prompt('Số hàng:', '3'));
    const cols = parseInt(prompt('Số cột:', '3'));

    if (rows && cols) {
        let table = '\\n\\n| ';

        // Header
        for (let i = 0; i < cols; i++) {
            table += `Cột ${i + 1} | `;
        }
        table += '\\n| ';

        // Separator
        for (let i = 0; i < cols; i++) {
            table += '--- | ';
        }
        table += '\\n';

        // Rows
        for (let r = 0; r < rows - 1; r++) {
            table += '| ';
            for (let c = 0; c < cols; c++) {
                table += 'Dữ liệu | ';
            }
            table += '\\n';
        }
        table += '\\n';

        formatText(table, '');
    }
}

function insertTOC() {
    const toc = `\\n\\n## Mục lục\\n\\n- [Phần 1](#phan-1)\\n- [Phần 2](#phan-2)\\n- [Phần 3](#phan-3)\\n\\n`;
    formatText(toc, '');
}

function insertSeparator() {
    formatText('\\n\\n---\\n\\n', '');
}

function insertCodeBlock() {
    const language = prompt('Nhập ngôn ngữ lập trình (javascript, python, html, css...):', 'javascript');
    const code = prompt('Nhập code:');
    if (code) {
        formatText(`\\n\\n\`\`\`${language}\\n${code}\\n\`\`\`\\n\\n`, '');
    }
}

function insertMath() {
    const formula = prompt('Nhập công thức LaTeX:');
    if (formula) {
        formatText(`$$${formula}$$`, '');
    }
}

function insertEmoji() {
    const emojis = ['😀', '😊', '😍', '🤔', '😢', '😡', '👍', '👎', '❤️', '🔥', '💡', '⭐', '🎉', '🙏', '💪'];
    const emoji = emojis[Math.floor(Math.random() * emojis.length)];
    formatText(emoji, '');
}

function insertDate() {
    const now = new Date();
    const date = now.toLocaleDateString('vi-VN');
    formatText(date, '');
}

function clearFormatting() {
    const textarea = document.getElementById('content-editor');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);

    // Remove common markdown formatting
    const cleaned = selectedText
        .replace(/\*\*(.*?)\*\*/g, '$1')  // Bold
        .replace(/\*(.*?)\*/g, '$1')      // Italic
        .replace(/~~(.*?)~~/g, '$1')      // Strikethrough
        .replace(/`(.*?)`/g, '$1')        // Code
        .replace(/\[(.*?)\]\(.*?\)/g, '$1'); // Links

    textarea.value = textarea.value.substring(0, start) + cleaned + textarea.value.substring(end);
    textarea.setSelectionRange(start, start + cleaned.length);
    textarea.focus();
}

// ===== PREVIEW AND DISPLAY FUNCTIONS =====
function togglePreview() {
    const textarea = document.getElementById('content-editor');
    const previewDiv = document.getElementById('content-preview');
    const previewBtn = document.getElementById('preview-btn');

    if (!isPreviewMode) {
        // Show preview
        updatePreview();
        previewDiv.style.display = 'block';
        textarea.style.display = 'none';
        previewBtn.innerHTML = '<i class="fas fa-edit"></i> Edit';
        previewBtn.classList.remove('btn-outline-info');
        previewBtn.classList.add('btn-info', 'text-white');
        isPreviewMode = true;
    } else {
        // Show editor
        previewDiv.style.display = 'none';
        textarea.style.display = 'block';
        previewBtn.innerHTML = '<i class="fas fa-eye"></i> Preview';
        previewBtn.classList.remove('btn-info', 'text-white');
        previewBtn.classList.add('btn-outline-info');
        isPreviewMode = false;
    }
}

function toggleSideBySide() {
    const textarea = document.getElementById('content-editor');
    const previewDiv = document.getElementById('content-preview');
    const sideBySideBtn = document.getElementById('side-by-side-btn');
    const container = textarea.parentElement;

    if (!isSideBySideMode) {
        // Enable side-by-side
        container.style.display = 'flex';
        container.style.gap = '1rem';
        textarea.style.width = '50%';
        textarea.style.display = 'block';
        previewDiv.style.width = '50%';
        previewDiv.style.display = 'block';
        updatePreview();

        sideBySideBtn.innerHTML = '<i class="fas fa-expand-alt"></i> Full';
        sideBySideBtn.classList.remove('btn-outline-secondary');
        sideBySideBtn.classList.add('btn-secondary', 'text-white');
        isSideBySideMode = true;
        isPreviewMode = false;
    } else {
        // Disable side-by-side
        container.style.display = 'block';
        textarea.style.width = '100%';
        previewDiv.style.display = 'none';

        sideBySideBtn.innerHTML = '<i class="fas fa-columns"></i> Side by Side';
        sideBySideBtn.classList.remove('btn-secondary', 'text-white');
        sideBySideBtn.classList.add('btn-outline-secondary');
        isSideBySideMode = false;
    }
}

function updatePreview() {
    const textarea = document.getElementById('content-editor');
    const previewDiv = document.getElementById('content-preview');

    if (typeof marked !== 'undefined') {
        // Configure marked options
        marked.setOptions({
            breaks: true,
            gfm: true,
            sanitize: false
        });

        let content = textarea.value;

        // Process custom elements
        content = processCustomElements(content);

        previewDiv.innerHTML = marked.parse(content);

        // Apply custom styling to preview
        previewDiv.classList.add('post-content-body');
    } else {
        previewDiv.innerHTML = '<p class="text-danger">Marked.js chưa được tải. Vui lòng thử lại.</p>';
    }
}

function processCustomElements(content) {
    // Process video containers
    content = content.replace(
        /<div class="video-container">\s*<iframe[^>]*src="([^"]*)"[^>]*><\/iframe>\s*<\/div>/g,
        '<div class="video-container"><iframe src="$1" frameborder="0" allowfullscreen></iframe></div>'
    );

    // Process image galleries
    content = content.replace(
        /<div class="image-gallery">([\s\S]*?)<\/div>/g,
        '<div class="row image-gallery">$1</div>'
    );

    // Process gallery images
    content = content.replace(
        /<img src="([^"]*)" class="gallery-img"[^>]*>/g,
        '<div class="col-md-4 mb-3"><img src="$1" class="img-fluid rounded" alt="Gallery image"></div>'
    );

    return content;
}

function toggleFullscreen() {
    const editorContainer = document.querySelector('.card');
    const fullscreenBtn = document.querySelector('[onclick="toggleFullscreen()"]');

    if (!isFullscreenMode) {
        editorContainer.style.position = 'fixed';
        editorContainer.style.top = '0';
        editorContainer.style.left = '0';
        editorContainer.style.width = '100vw';
        editorContainer.style.height = '100vh';
        editorContainer.style.zIndex = '9999';
        editorContainer.style.margin = '0';
        editorContainer.style.borderRadius = '0';

        const textarea = document.getElementById('content-editor');
        textarea.style.height = 'calc(100vh - 300px)';

        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
        fullscreenBtn.title = 'Thoát toàn màn hình';
        isFullscreenMode = true;
    } else {
        editorContainer.style.position = 'static';
        editorContainer.style.width = 'auto';
        editorContainer.style.height = 'auto';
        editorContainer.style.zIndex = 'auto';
        editorContainer.style.margin = '';
        editorContainer.style.borderRadius = '';

        const textarea = document.getElementById('content-editor');
        textarea.style.height = 'auto';

        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
        fullscreenBtn.title = 'Chế độ toàn màn hình';
        isFullscreenMode = false;
    }
}

// Word count
function updateWordCount() {
    const textarea = document.getElementById('content-editor');
    const wordCount = textarea.value.split(/\s+/).filter(word => word.length > 0).length;
    const charCount = textarea.value.length;
    const readingTime = Math.ceil(wordCount / 200); // Average reading speed

    document.getElementById('word-count').textContent = `${wordCount} từ, ${charCount} ký tự, ~${readingTime} phút đọc`;
}

// Help function
function showHelp() {
    const helpContent = `
    <div class="help-content">
        <h5><i class="fas fa-book"></i> Hướng dẫn sử dụng Editor</h5>

        <div class="row">
            <div class="col-md-6">
                <h6><i class="fas fa-keyboard"></i> Phím tắt</h6>
                <ul class="list-unstyled">
                    <li><kbd>Ctrl+B</kbd> - In đậm</li>
                    <li><kbd>Ctrl+I</kbd> - In nghiêng</li>
                    <li><kbd>Ctrl+K</kbd> - Chèn link</li>
                    <li><kbd>Ctrl+P</kbd> - Preview</li>
                    <li><kbd>Ctrl+Enter</kbd> - Fullscreen</li>
                    <li><kbd>Tab</kbd> - Thụt lề</li>
                </ul>

                <h6><i class="fas fa-magic"></i> Markdown cơ bản</h6>
                <ul class="list-unstyled">
                    <li><code>**text**</code> - In đậm</li>
                    <li><code>*text*</code> - In nghiêng</li>
                    <li><code>~~text~~</code> - Gạch ngang</li>
                    <li><code># Tiêu đề</code> - H1</li>
                    <li><code>## Tiêu đề</code> - H2</li>
                    <li><code>- Item</code> - Danh sách</li>
                    <li><code>> Quote</code> - Trích dẫn</li>
                </ul>
            </div>

            <div class="col-md-6">
                <h6><i class="fas fa-tools"></i> Tính năng nâng cao</h6>
                <ul class="list-unstyled">
                    <li><i class="fab fa-youtube text-danger"></i> Chèn video YouTube</li>
                    <li><i class="fas fa-images text-primary"></i> Tạo gallery hình ảnh</li>
                    <li><i class="fas fa-table text-success"></i> Chèn bảng</li>
                    <li><i class="fas fa-code text-warning"></i> Code blocks</li>
                    <li><i class="fas fa-anchor text-info"></i> Liên kết nội bộ</li>
                    <li><i class="fas fa-exclamation-triangle text-warning"></i> Hộp thông báo</li>
                    <li><i class="fas fa-tasks text-secondary"></i> Checkbox lists</li>
                    <li><i class="fas fa-music text-purple"></i> File âm thanh</li>
                </ul>

                <h6><i class="fas fa-lightbulb"></i> Mẹo hay</h6>
                <ul class="list-unstyled">
                    <li>• Sử dụng Preview để kiểm tra</li>
                    <li>• Side-by-side để edit và xem cùng lúc</li>
                    <li>• Fullscreen để tập trung viết</li>
                    <li>• Rê chuột vào nút để xem tooltip</li>
                </ul>
            </div>
        </div>

        <div class="mt-3 p-3 bg-light rounded">
            <h6><i class="fas fa-star text-warning"></i> Ví dụ nhanh</h6>
            <p class="mb-1"><strong>Chèn video YouTube:</strong> Nhấn nút YouTube, dán link video</p>
            <p class="mb-1"><strong>Tạo bảng:</strong> Nhấn nút Table, chọn số hàng/cột</p>
            <p class="mb-0"><strong>Hộp thông báo:</strong> Nhấn nút Warning, chọn loại và nhập nội dung</p>
        </div>
    </div>
    `;

    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Hướng dẫn Editor</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    ${helpContent}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Remove modal after hiding
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

// Auto-save functionality (optional)
let autoSaveTimer;
function autoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(() => {
        const content = document.getElementById('content-editor').value;
        localStorage.setItem('post-draft', content);
        console.log('Draft auto-saved');
    }, 5000); // Save after 5 seconds of inactivity
}

// Load draft on page load
document.addEventListener('DOMContentLoaded', function() {
    const draft = localStorage.getItem('post-draft');
    if (draft && confirm('Có bản nháp được lưu. Bạn có muốn khôi phục không?')) {
        document.getElementById('content-editor').value = draft;
        updateWordCount();
    }
});

function resizeImage(filename) {
    const width = document.getElementById('image-width').value;
    const height = document.getElementById('image-height').value;
    const quality = document.getElementById('image-quality').value;

    if (!width || !height) {
        alert('Vui lòng nhập kích thước!');
        return;
    }

    // Show loading
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang resize...';
    btn.disabled = true;

    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

    fetch('/resize_image', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken || ''
        },
        body: JSON.stringify({
            filename: filename,
            width: parseInt(width),
            height: parseInt(height),
            quality: parseInt(quality)
        })
    })
    .then(response => response.json())
    .then(data => {
        btn.innerHTML = originalText;
        btn.disabled = false;

        if (data.success) {
            // Update preview image
            document.getElementById('preview-image').src = data.url + '?t=' + Date.now();
            alert('Resize thành công!');
        } else {
            alert('Resize thất bại: ' + data.error);
        }
    })
    .catch(error => {
        btn.innerHTML = originalText;
        btn.disabled = false;
        alert('Lỗi resize: ' + error.message);
    });
}

function previewImageCode() {
    const alt = document.getElementById('image-alt').value;
    const width = document.getElementById('image-width').value;
    const height = document.getElementById('image-height').value;
    const align = document.getElementById('image-align').value;
    const responsive = document.getElementById('image-responsive').checked;
    const rounded = document.getElementById('image-rounded').checked;
    const shadow = document.getElementById('image-shadow').checked;

    let classes = [];
    if (responsive) classes.push('img-fluid');
    if (rounded) classes.push('rounded');
    if (shadow) classes.push('shadow');

    let style = '';
    if (width && height) {
        style += `width: ${width}px; height: ${height}px; `;
    }
    if (align !== 'none') {
        if (align === 'center') {
            style += 'display: block; margin: 0 auto; ';
        } else {
            style += `float: ${align}; margin: 10px; `;
        }
    }

    const imageUrl = document.getElementById('preview-image').src;
    const code = `<img src="${imageUrl}" alt="${alt}" class="${classes.join(' ')}" style="${style}">`;

    // Show code preview
    const codeModal = document.createElement('div');
    codeModal.className = 'modal fade';
    codeModal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">HTML Code Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <pre><code>${code}</code></pre>
                    <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard('${code.replace(/'/g, "\\'")}')">
                        <i class="fas fa-copy"></i> Copy Code
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(codeModal);
    const bsCodeModal = new bootstrap.Modal(codeModal);
    bsCodeModal.show();

    codeModal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(codeModal);
    });
}

function insertImageCode(filename, originalUrl) {
    const alt = document.getElementById('image-alt').value;
    const width = document.getElementById('image-width').value;
    const height = document.getElementById('image-height').value;
    const align = document.getElementById('image-align').value;
    const responsive = document.getElementById('image-responsive').checked;
    const rounded = document.getElementById('image-rounded').checked;
    const shadow = document.getElementById('image-shadow').checked;

    let classes = [];
    if (responsive) classes.push('img-fluid');
    if (rounded) classes.push('rounded');
    if (shadow) classes.push('shadow');

    let style = '';
    if (width && height) {
        style += `width: ${width}px; height: ${height}px; `;
    }
    if (align !== 'none') {
        if (align === 'center') {
            style += 'display: block; margin: 0 auto; ';
        } else {
            style += `float: ${align}; margin: 10px; `;
        }
    }

    const imageUrl = document.getElementById('preview-image').src;
    const imageCode = `\\n\\n<img src="${imageUrl}" alt="${alt}" class="${classes.join(' ')}" style="${style}">\\n\\n`;

    // Insert into editor
    formatText(imageCode, '');

    // Close modal
    const modal = document.querySelector('.modal.show');
    if (modal) {
        bootstrap.Modal.getInstance(modal).hide();
    }

    // Show success message
    showToast('Đã chèn hình ảnh vào bài viết!', 'success');
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showToast('Đã copy code!', 'success');
    }).catch(function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('Đã copy code!', 'success');
    });
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    toastContainer.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after hiding
    toast.addEventListener('hidden.bs.toast', function() {
        toastContainer.removeChild(toast);
    });
}

// Add auto-save to input event
document.getElementById('content-editor').addEventListener('input', autoSave);
</script>
{% endblock %}
