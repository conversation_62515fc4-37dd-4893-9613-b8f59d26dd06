{% extends "base.html" %}

{% block title %}{% if post %}Sửa bài viết{% else %}Tạo bài viết mới{% endif %} - Đ<PERSON><PERSON>{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4>
                    <i class="fas fa-{% if post %}edit{% else %}plus{% endif %}"></i>
                    {% if post %}Sửa bài viết{% else %}Tạo bài viết mới{% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.title.label(class="form-label") }}
                        {{ form.title(class="form-control") }}
                        {% if form.title.errors %}
                            <div class="text-danger small">
                                {% for error in form.title.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.summary.label(class="form-label") }}
                            {{ form.summary(class="form-control") }}
                            <div class="form-text">Tóm tắt ngắn gọn về nội dung bài viết</div>
                            {% if form.summary.errors %}
                                <div class="text-danger small">
                                    {% for error in form.summary.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.category_id.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-tags"></i>
                                </span>
                                {{ form.category_id(class="form-control") }}
                            </div>
                            <div class="form-text">Chọn chủ đề phù hợp cho bài viết</div>
                            {% if form.category_id.errors %}
                                <div class="text-danger small">
                                    {% for error in form.category_id.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Featured Image Upload -->
                    <div class="mb-3">
                        {{ form.featured_image.label(class="form-label") }}
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-image"></i>
                            </span>
                            {{ form.featured_image(class="form-control") }}
                        </div>
                        <div class="form-text">Chọn hình ảnh đại diện cho bài viết (JPG, PNG, GIF)</div>
                        {% if form.featured_image.errors %}
                            <div class="text-danger small">
                                {% for error in form.featured_image.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.content.label(class="form-label") }}
                        {{ form.content(class="form-control", rows="15", id="content-editor") }}
                        <div class="form-text">Nội dung chi tiết của bài viết (hỗ trợ Markdown)</div>
                        {% if form.content.errors %}
                            <div class="text-danger small">
                                {% for error in form.content.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- SEO Settings -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-search me-2"></i>Cài đặt SEO
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.meta_title.label(class="form-label") }}
                                    {{ form.meta_title(class="form-control") }}
                                    <div class="form-text">Tiêu đề SEO (tối đa 60 ký tự)</div>
                                    {% if form.meta_title.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.meta_title.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="col-md-6 mb-3">
                                    {{ form.keywords.label(class="form-label") }}
                                    {{ form.keywords(class="form-control") }}
                                    <div class="form-text">Từ khóa SEO (phân cách bằng dấu phẩy)</div>
                                    {% if form.keywords.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.keywords.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                {{ form.meta_description.label(class="form-label") }}
                                {{ form.meta_description(class="form-control", rows="3") }}
                                <div class="form-text">Mô tả SEO (tối đa 160 ký tự)</div>
                                {% if form.meta_description.errors %}
                                    <div class="text-danger small">
                                        {% for error in form.meta_description.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Publishing Options -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-cog me-2"></i>Tùy chọn xuất bản
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        {{ form.is_published(class="form-check-input") }}
                                        {{ form.is_published.label(class="form-check-label") }}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        {{ form.is_featured(class="form-check-input") }}
                                        {{ form.is_featured.label(class="form-check-label") }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('admin') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Simple formatting buttons for the content editor
document.addEventListener('DOMContentLoaded', function() {
    const contentEditor = document.getElementById('content-editor');
    
    // Add formatting toolbar
    const toolbar = document.createElement('div');
    toolbar.className = 'btn-toolbar mb-2';
    toolbar.innerHTML = `
        <div class="btn-group btn-group-sm me-2">
            <button type="button" class="btn btn-outline-secondary" onclick="formatText('**', '**')" title="In đậm">
                <i class="fas fa-bold"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="formatText('*', '*')" title="In nghiêng">
                <i class="fas fa-italic"></i>
            </button>
        </div>
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-secondary" onclick="formatText('\\n## ', '')" title="Tiêu đề">
                <i class="fas fa-heading"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="formatText('\\n- ', '')" title="Danh sách">
                <i class="fas fa-list"></i>
            </button>
        </div>
    `;
    
    contentEditor.parentNode.insertBefore(toolbar, contentEditor);
});

function formatText(before, after) {
    const textarea = document.getElementById('content-editor');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    
    const newText = before + selectedText + after;
    textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);
    
    // Set cursor position
    const newCursorPos = start + before.length + selectedText.length + after.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();
}
</script>
{% endblock %}
