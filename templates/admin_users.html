{% extends "base.html" %}

{% block title %}Quản lý Users - Admin{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users me-2"></i>Quản lý Users</h2>
                <a href="{{ url_for('create_user') }}" class="btn btn-warning">
                    <i class="fas fa-user-plus me-2"></i>Tạo User mới
                </a>
            </div>

            <div class="card shadow-sm">
                <div class="card-body">
                    {% if users %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Tên đăng nhập</th>
                                        <th>Họ tên</th>
                                        <th>Email</th>
                                        <th><PERSON><PERSON><PERSON><PERSON></th>
                                        <th><PERSON><PERSON><PERSON> t<PERSON></th>
                                        <th>Số bài viết</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in users %}
                                    <tr>
                                        <td>{{ user.id }}</td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-2">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                                <strong>{{ user.username }}</strong>
                                            </div>
                                        </td>
                                        <td>{{ user.full_name }}</td>
                                        <td>{{ user.email }}</td>
                                        <td>
                                            {% if user.is_admin %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-crown me-1"></i>Admin
                                                </span>
                                            {% else %}
                                                <span class="badge bg-primary">
                                                    <i class="fas fa-user me-1"></i>User
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>{{ user.created_at.strftime('%d/%m/%Y') }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ user.posts|length }}</span>
                                        </td>
                                        <td>
                                            {% if user.is_active %}
                                                <span class="badge bg-success">Hoạt động</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Tạm khóa</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                {% if user.id != current_user.id %}
                                                    <a href="{{ url_for('reset_user_password', user_id=user.id) }}"
                                                       class="btn btn-outline-info" title="Đặt lại mật khẩu">
                                                        <i class="fas fa-key"></i>
                                                    </a>
                                                    <button class="btn btn-outline-warning" title="Đổi trạng thái"
                                                            onclick="toggleUserStatus({{ user.id }})">
                                                        <i class="fas fa-toggle-{{ 'on' if user.is_active else 'off' }}"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger" title="Xóa"
                                                            onclick="deleteUser({{ user.id }})">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Chưa có user nào</h5>
                            <p class="text-muted">Hãy tạo user đầu tiên!</p>
                            <a href="{{ url_for('create_user') }}" class="btn btn-warning">
                                <i class="fas fa-user-plus me-2"></i>Tạo User mới
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleUserStatus(userId) {
    if (confirm('Bạn có chắc chắn muốn thay đổi trạng thái user này?')) {
        fetch(`/admin/toggle_user_status/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra!');
            }
        });
    }
}

function deleteUser(userId) {
    if (confirm('Bạn có chắc chắn muốn xóa user này? Hành động này không thể hoàn tác!')) {
        fetch(`/admin/delete_user/${userId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Có lỗi xảy ra!');
            }
        });
    }
}
</script>
{% endblock %}
