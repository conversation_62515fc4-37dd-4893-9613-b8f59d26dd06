{% extends "base.html" %}

{% block title %}Đặt lại mật khẩu - {{ user.username }} - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-shield me-2"></i>Đặt lại mật khẩu cho {{ user.username }}
                    </h4>
                </div>
                <div class="card-body">
                    <!-- User Info -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user me-2"></i>Thông tin tài khoản</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>Tên đăng nhập:</strong><br>
                                {{ user.username }}
                            </div>
                            <div class="col-md-4">
                                <strong>Email:</strong><br>
                                {{ user.email }}
                            </div>
                            <div class="col-md-4">
                                <strong>Họ và tên:</strong><br>
                                {{ user.full_name }}
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.username.label(class="form-label") }}
                                    {{ form.username(class="form-control", readonly=true) }}
                                    <div class="form-text">Xác nhận tên đăng nhập</div>
                                    {% if form.username.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.username.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control", readonly=true) }}
                                    <div class="form-text">Xác nhận email</div>
                                    {% if form.email.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.email.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.new_password.label(class="form-label") }}
                                    {{ form.new_password(class="form-control") }}
                                    <div class="form-text">Mật khẩu mới phải có ít nhất 6 ký tự</div>
                                    {% if form.new_password.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.new_password.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.confirm_password.label(class="form-label") }}
                                    {{ form.confirm_password(class="form-control") }}
                                    {% if form.confirm_password.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.confirm_password.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('manage_users') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>Hủy
                            </a>
                            {{ form.submit(class="btn btn-danger") }}
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Security Warning -->
            <div class="alert alert-warning mt-3">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Cảnh báo bảo mật</h6>
                <ul class="mb-0">
                    <li><strong>Hành động này sẽ được ghi lại trong blockchain logs</strong></li>
                    <li>Mật khẩu mới sẽ được mã hóa bằng công nghệ blockchain</li>
                    <li>Người dùng sẽ cần sử dụng mật khẩu mới để đăng nhập</li>
                    <li>Không thể hoàn tác sau khi thực hiện</li>
                </ul>
            </div>
            
            <!-- Admin Responsibility -->
            <div class="alert alert-danger mt-3">
                <h6><i class="fas fa-user-shield me-2"></i>Trách nhiệm quản trị viên</h6>
                <p class="mb-0">
                    Bằng việc đặt lại mật khẩu này, bạn xác nhận rằng đã xác minh danh tính của người dùng 
                    và có quyền thực hiện hành động này. Hành động sẽ được ghi lại với IP: 
                    <code>{{ request.remote_addr }}</code>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
