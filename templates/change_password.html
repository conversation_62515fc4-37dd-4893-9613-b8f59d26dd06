{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON> mật khẩu - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-key me-2"></i><PERSON><PERSON><PERSON> mật khẩu
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.current_password.label(class="form-label") }}
                            {{ form.current_password(class="form-control") }}
                            {% if form.current_password.errors %}
                                <div class="text-danger small">
                                    {% for error in form.current_password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.new_password.label(class="form-label") }}
                            {{ form.new_password(class="form-control") }}
                            <div class="form-text">Mật khẩu phải có ít nhất 6 ký tự</div>
                            {% if form.new_password.errors %}
                                <div class="text-danger small">
                                    {% for error in form.new_password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.confirm_password.label(class="form-label") }}
                            {{ form.confirm_password(class="form-control") }}
                            {% if form.confirm_password.errors %}
                                <div class="text-danger small">
                                    {% for error in form.confirm_password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-warning") }}
                        </div>
                    </form>
                    
                    <div class="mt-3 text-center">
                        <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Quay lại Profile
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Security Notice -->
            <div class="alert alert-info mt-3">
                <h6><i class="fas fa-shield-alt me-2"></i>Bảo mật nâng cao</h6>
                <p class="mb-0">Mật khẩu của bạn được mã hóa bằng công nghệ blockchain để đảm bảo an toàn tuyệt đối.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
