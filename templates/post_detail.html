{% extends "base.html" %}

{% block title %}{{ post.meta_title or post.title }} - Phật <PERSON>{% endblock %}

{% block head %}
<!-- SEO Meta Tags -->
<meta name="description" content="{{ post.meta_description or post.summary }}">
<meta name="keywords" content="{{ post.keywords }}">
<meta name="author" content="{{ post.author.full_name or post.author.username }}">

<!-- AMP Link -->
<link rel="amphtml" href="{{ url_for('post_detail_amp', slug=post.slug, _external=True) }}">

<!-- Open Graph -->
<meta property="og:title" content="{{ post.meta_title or post.title }}">
<meta property="og:description" content="{{ post.meta_description or post.summary }}">
<meta property="og:type" content="article">
<meta property="og:url" content="{{ url_for('post_detail', slug=post.slug, _external=True) }}">
{% if post.featured_image %}
<meta property="og:image" content="{{ url_for('uploaded_file', filename=post.featured_image, _external=True) }}">
{% endif %}

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ post.meta_title or post.title }}">
<meta name="twitter:description" content="{{ post.meta_description or post.summary }}">
{% if post.featured_image %}
<meta name="twitter:image" content="{{ url_for('uploaded_file', filename=post.featured_image, _external=True) }}">
{% endif %}

<!-- Structured Data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": "{{ post.title }}",
    "description": "{{ post.meta_description or post.summary }}",
    "author": {
        "@type": "Person",
        "name": "{{ post.author.full_name or post.author.username }}"
    },
    "publisher": {
        "@type": "Organization",
        "name": "Phật Học Platform"
    },
    "datePublished": "{{ post.created_at.isoformat() }}",
    "dateModified": "{{ post.updated_at.isoformat() }}",
    {% if post.featured_image %}
    "image": "{{ url_for('uploaded_file', filename=post.featured_image, _external=True) }}",
    {% endif %}
    "mainEntityOfPage": "{{ url_for('post_detail', slug=post.slug, _external=True) }}"
}
</script>
{% endblock %}

{% block content %}
<!-- Post Header -->
<div class="post-header-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">Trang chủ</a></li>
                        <li class="breadcrumb-item">
                            <a href="{{ url_for('category_posts', slug=post.category.slug) }}">{{ post.category.name }}</a>
                        </li>
                        <li class="breadcrumb-item active">{{ post.title }}</li>
                    </ol>
                </nav>

                <!-- Category Badge -->
                <div class="mb-3">
                    <span class="category-badge-large" style="background-color: {{ post.category.color }};">
                        <i class="{{ post.category.icon }} me-2"></i>{{ post.category.name }}
                    </span>
                    {% if post.is_featured %}
                        <span class="featured-badge-large ms-2">
                            <i class="fas fa-star me-1"></i>Nổi bật
                        </span>
                    {% endif %}
                </div>

                <!-- Post Title -->
                <h1 class="post-title-large mb-4">{{ post.title }}</h1>

                <!-- Post Meta -->
                <div class="post-meta-section mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="author-avatar-large me-3">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ post.author.full_name or post.author.username }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>{{ post.created_at.strftime('%d/%m/%Y') }}
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-clock me-1"></i>{{ ((post.content|length / 200) | round) }} phút đọc
                                        {% if post.updated_at > post.created_at %}
                                            <span class="mx-2">•</span>
                                            <i class="fas fa-edit me-1"></i>Cập nhật {{ post.updated_at.strftime('%d/%m/%Y') }}
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="post-stats-large">
                                <span class="stat-item">
                                    <i class="fas fa-eye"></i> {{ post.view_count }}
                                </span>
                                <span class="stat-item">
                                    <i class="fas fa-comments"></i> {{ approved_comments|length }}
                                </span>
                                {% if current_user.is_authenticated and (current_user.id == post.user_id or current_user.is_admin) %}
                                    <div class="dropdown d-inline-block">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="{{ url_for('edit_post', id=post.id) }}">
                                                <i class="fas fa-edit me-2"></i>Chỉnh sửa
                                            </a></li>
                                            {% if current_user.is_admin %}
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deletePost({{ post.id }})">
                                                    <i class="fas fa-trash me-2"></i>Xóa
                                                </a></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                {% if post.featured_image %}
                <div class="featured-image-container mb-5">
                    <img src="{{ url_for('uploaded_file', filename=post.featured_image) }}"
                         class="img-fluid rounded-3 shadow" alt="{{ post.title }}"
                         style="width: 100%; height: 400px; object-fit: cover;">
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Post Content -->
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <article class="post-content-wrapper">
                <div class="post-content-body">
                    {{ post.content|safe }}
                </div>

                <!-- Post Tags/Keywords -->
                {% if post.keywords %}
                <div class="post-tags mt-5 pt-4 border-top">
                    <h6 class="mb-3"><i class="fas fa-tags me-2"></i>Từ khóa:</h6>
                    <div class="tags-container">
                        {% for keyword in post.keywords.split(',') %}
                            <span class="tag-item">{{ keyword.strip() }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Social Share -->
                <div class="social-share mt-4 pt-4 border-top">
                    <h6 class="mb-3"><i class="fas fa-share-alt me-2"></i>Chia sẻ bài viết:</h6>
                    <div class="share-buttons">
                        <a href="#" class="btn btn-outline-primary btn-sm me-2" onclick="shareOnFacebook()">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </a>
                        <a href="#" class="btn btn-outline-info btn-sm me-2" onclick="shareOnTwitter()">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="#" class="btn btn-outline-success btn-sm me-2" onclick="copyLink()">
                            <i class="fas fa-link me-1"></i>Copy Link
                        </a>
                    </div>
                </div>
            </article>

            <!-- Related Posts Section - Below Main Content -->
            <div class="related-posts-section mt-5">
                <div class="card shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-bookmark me-2"></i>Bài viết liên quan
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if related_posts and related_posts|length > 0 %}
                            <div class="row">
                                {% for related_post in related_posts %}
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="related-post-card">
                                        {% if related_post.featured_image %}
                                        <div class="related-post-image mb-3">
                                            <img src="{{ url_for('uploaded_file', filename=related_post.featured_image) }}"
                                                 class="img-fluid rounded" alt="{{ related_post.title }}"
                                                 style="height: 150px; width: 100%; object-fit: cover;">
                                        </div>
                                        {% endif %}

                                        <div class="related-post-content">
                                            <div class="mb-2">
                                                <span class="badge" style="background-color: {{ related_post.category.color }}; color: white;">
                                                    <i class="{{ related_post.category.icon }} me-1"></i>{{ related_post.category.name }}
                                                </span>
                                            </div>

                                            <h6 class="related-post-title mb-2">
                                                <a href="{{ url_for('post_detail', slug=related_post.slug) }}" class="text-decoration-none text-dark">
                                                    {{ related_post.title }}
                                                </a>
                                            </h6>

                                            {% if related_post.summary %}
                                                <p class="related-post-excerpt text-muted small mb-2">
                                                    {{ related_post.summary[:100] }}...
                                                </p>
                                            {% endif %}

                                            <div class="related-post-meta">
                                                <small class="text-muted">
                                                    <i class="fas fa-user me-1"></i>{{ related_post.author.full_name or related_post.author.username }}
                                                    <span class="mx-2">•</span>
                                                    <i class="fas fa-calendar me-1"></i>{{ related_post.created_at.strftime('%d/%m/%Y') }}
                                                    <span class="mx-2">•</span>
                                                    <i class="fas fa-eye me-1"></i>{{ related_post.view_count }}
                                                </small>
                                            </div>

                                            <div class="mt-3">
                                                <a href="{{ url_for('post_detail', slug=related_post.slug) }}" class="btn btn-outline-warning btn-sm">
                                                    <i class="fas fa-arrow-right me-1"></i>Đọc thêm
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>

                            <!-- View All Category Posts -->
                            <div class="text-center mt-4 pt-3 border-top">
                                <a href="{{ url_for('category_posts', slug=post.category.slug) }}" class="btn btn-warning">
                                    <i class="fas fa-folder-open me-2"></i>Xem tất cả bài viết {{ post.category.name }}
                                </a>
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-bookmark fa-4x text-muted mb-3"></i>
                                <h6 class="text-muted mb-3">Không có bài viết liên quan</h6>
                                <p class="text-muted mb-4">Hãy khám phá các bài viết khác trong danh mục này</p>
                                <a href="{{ url_for('category_posts', slug=post.category.slug) }}" class="btn btn-outline-warning">
                                    <i class="fas fa-folder me-2"></i>Xem danh mục {{ post.category.name }}
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

        <!-- Comments Section -->
        <div class="mt-4">
            <h4><i class="fas fa-comments"></i> Bình luận ({{ approved_comments|length }})</h4>
            
            <!-- Comment Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6>Để lại bình luận</h6>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ comment_form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ comment_form.author_name.label(class="form-label") }}
                            {{ comment_form.author_name(class="form-control") }}
                            {% if comment_form.author_name.errors %}
                                <div class="text-danger small">
                                    {% for error in comment_form.author_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ comment_form.content.label(class="form-label") }}
                            {{ comment_form.content(class="form-control", rows="4") }}
                            {% if comment_form.content.errors %}
                                <div class="text-danger small">
                                    {% for error in comment_form.content.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        {{ comment_form.submit(class="btn btn-primary") }}
                    </form>
                </div>
            </div>

            <!-- Comments List -->
            {% if approved_comments %}
                {% for comment in approved_comments %}
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title mb-1">{{ comment.author_name }}</h6>
                                <small class="text-muted">{{ comment.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                            </div>
                            {% if current_user.is_authenticated and current_user.is_admin %}
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteComment({{ comment.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            {% endif %}
                        </div>
                        <p class="card-text mt-2">{{ comment.content }}</p>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-comment-slash fa-2x text-muted mb-2"></i>
                    <p class="text-muted">Chưa có bình luận nào. Hãy là người đầu tiên bình luận!</p>
                </div>
            {% endif %}
            <!-- Comments Section -->
            <div class="comments-section mt-5">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Bình luận ({{ approved_comments|length }})
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if approved_comments %}
                            {% for comment in approved_comments %}
                            <div class="comment-item mb-4 pb-3 {% if not loop.last %}border-bottom{% endif %}">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div class="d-flex align-items-center">
                                        <div class="comment-avatar me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ comment.author_name }}</h6>
                                            <small class="text-muted">{{ comment.created_at.strftime('%d/%m/%Y %H:%M') }}</small>
                                        </div>
                                    </div>
                                </div>
                                <p class="comment-content mb-0">{{ comment.content }}</p>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Chưa có bình luận nào. Hãy là người đầu tiên bình luận!</p>
                            </div>
                        {% endif %}

                        <!-- Comment Form -->
                        <div class="comment-form mt-4 pt-4 border-top">
                            <h6 class="mb-3">
                                <i class="fas fa-pen me-2"></i>Để lại bình luận
                            </h6>
                            <form method="POST" action="{{ url_for('add_comment', slug=post.slug) }}">
                                {{ comment_form.hidden_tag() }}
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        {{ comment_form.author_name.label(class="form-label") }}
                                        {{ comment_form.author_name(class="form-control") }}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    {{ comment_form.content.label(class="form-label") }}
                                    {{ comment_form.content(class="form-control", rows="4", placeholder="Chia sẻ suy nghĩ của bạn về bài viết...") }}
                                </div>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-paper-plane me-2"></i>Gửi bình luận
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>
{% endblock %}
