{% extends "base.html" %}

{% block title %}Quên mật khẩu - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>Quên mật khẩu
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Hướng dẫn</h6>
                        <p class="mb-0">
                            Nhập thông tin tài khoản của bạn. Yêu cầu đặt lại mật khẩu sẽ được gửi đến quản trị viên 
                            để xử lý. Vui lòng liên hệ admin để được hỗ trợ nhanh nhất.
                        </p>
                    </div>
                    
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control") }}
                            <div class="form-text">Nhập tên đăng nhập của bạn</div>
                            {% if form.username.errors %}
                                <div class="text-danger small">
                                    {% for error in form.username.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control") }}
                            <div class="form-text">Nhập email đã đăng ký với tài khoản</div>
                            {% if form.email.errors %}
                                <div class="text-danger small">
                                    {% for error in form.email.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-info") }}
                        </div>
                    </form>
                    
                    <div class="mt-3 text-center">
                        <a href="{{ url_for('login') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Quay lại đăng nhập
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Contact Info -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-phone me-2"></i>Liên hệ hỗ trợ</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>Quản trị viên:</strong> <EMAIL></p>
                    <p class="mb-2"><strong>Hotline:</strong> 1900-xxxx</p>
                    <p class="mb-0"><strong>Thời gian hỗ trợ:</strong> 8:00 - 17:00 (Thứ 2 - Thứ 6)</p>
                </div>
            </div>
            
            <!-- Security Notice -->
            <div class="alert alert-warning mt-3">
                <h6><i class="fas fa-shield-alt me-2"></i>Bảo mật</h6>
                <ul class="mb-0">
                    <li>Yêu cầu sẽ được ghi lại trong hệ thống blockchain</li>
                    <li>Chỉ quản trị viên mới có thể đặt lại mật khẩu</li>
                    <li>Cần xác minh danh tính trước khi đặt lại</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
