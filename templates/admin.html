{% extends "base.html" %}

{% block title %}Q<PERSON>ả<PERSON> lý - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-cogs"></i> Trang quản lý</h2>
        <hr>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_posts }}</h4>
                        <p>Tổng bài viết</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_comments }}</h4>
                        <p>Tổng bình luận</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-comments fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ pending_comments }}</h4>
                        <p>Bình luận chờ duyệt</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ recent_posts }}</h4>
                        <p>Bài viết tuần này</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-week fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Blockchain Security Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt"></i> Bảo mật Blockchain
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-3">Hệ thống sử dụng công nghệ blockchain để đảm bảo tính toàn vẹn và bảo mật dữ liệu.</p>
                <div class="row">
                    <div class="col-md-6">
                        <a href="{{ url_for('blockchain_logs') }}" class="btn btn-warning btn-block">
                            <i class="fas fa-list"></i> Xem nhật ký Blockchain
                        </a>
                    </div>
                    <div class="col-md-6">
                        <button onclick="verifySystemIntegrity()" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-check-circle"></i> Kiểm tra toàn vẹn hệ thống
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list"></i> Quản lý bài viết</h5>
                <a href="{{ url_for('create_post') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Tạo mới
                </a>
            </div>
            <div class="card-body">
                {% if posts %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tiêu đề</th>
                                    <th>Ngày tạo</th>
                                    <th>Bình luận</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for post in posts %}
                                <tr>
                                    <td>
                                        <a href="{{ url_for('post_detail', slug=post.slug) }}" class="text-decoration-none">
                                            {{ post.title[:50] }}{% if post.title|length > 50 %}...{% endif %}
                                        </a>
                                    </td>
                                    <td>{{ post.created_at.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ post.comments|length }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('edit_post', id=post.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deletePost({{ post.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">Chưa có bài viết nào.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-comments"></i> Bình luận chờ duyệt</h5>
            </div>
            <div class="card-body">
                {% if pending_comments_list %}
                    {% for comment in pending_comments_list %}
                    <div class="border-bottom pb-2 mb-2">
                        <small class="text-muted">{{ comment.author_name }} - {{ comment.created_at.strftime('%d/%m/%Y') }}</small>
                        <p class="mb-1">{{ comment.content[:100] }}{% if comment.content|length > 100 %}...{% endif %}</p>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-success" onclick="approveComment({{ comment.id }})">
                                <i class="fas fa-check"></i> Duyệt
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteComment({{ comment.id }})">
                                <i class="fas fa-times"></i> Xóa
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">Không có bình luận chờ duyệt.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
function verifySystemIntegrity() {
    if (confirm('Bạn có muốn kiểm tra toàn vẹn dữ liệu của toàn bộ hệ thống?')) {
        window.location.href = '{{ url_for("blockchain_logs") }}';
    }
}
</script>
{% endblock %}
