{% extends "base.html" %}

{% block title %}H<PERSON> sơ - {{ current_user.full_name }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-body text-center">
                <div class="avatar-circle mx-auto mb-3">
                    <i class="fas fa-user fa-3x text-primary"></i>
                </div>
                <h4 class="card-title">{{ current_user.full_name }}</h4>
                <p class="text-muted">@{{ current_user.username }}</p>
                <p class="text-muted">
                    <i class="fas fa-envelope me-2"></i>{{ current_user.email }}
                </p>
                <p class="text-muted">
                    <i class="fas fa-calendar me-2"></i>
                    Tham gia từ {{ current_user.created_at.strftime('%d/%m/%Y') }}
                </p>
                {% if current_user.is_admin %}
                    <span class="badge bg-primary">
                        <i class="fas fa-crown me-1"></i>Quản trị viên
                    </span>
                {% else %}
                    <span class="badge bg-success">
                        <i class="fas fa-user me-1"></i>Thành viên
                    </span>
                {% endif %}

                <!-- Account Management Buttons -->
                <div class="mt-3">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('edit_profile') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-user-edit me-1"></i>Chỉnh sửa thông tin
                        </a>
                        <a href="{{ url_for('change_password') }}" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-key me-1"></i>Đổi mật khẩu
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow-sm mt-4">
            <div class="card-header">
                <h6><i class="fas fa-chart-bar me-2"></i>Thống kê</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ user_posts|length }}</h4>
                        <small class="text-muted">Bài viết</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">
                            {% set total_comments = 0 %}
                            {% for post in user_posts %}
                                {% set total_comments = total_comments + post.comments|length %}
                            {% endfor %}
                            {{ total_comments }}
                        </h4>
                        <small class="text-muted">Bình luận</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-file-alt me-2"></i>Bài viết của tôi</h5>
                <a href="{{ url_for('create_post') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-1"></i>Tạo bài viết mới
                </a>
            </div>
            <div class="card-body">
                {% if user_posts %}
                    {% for post in user_posts %}
                    <div class="post-item border-bottom pb-3 mb-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="{{ url_for('post_detail', slug=post.slug) }}" class="text-decoration-none">
                                        {{ post.title }}
                                    </a>
                                </h6>
                                {% if post.summary %}
                                    <p class="text-muted small mb-2">{{ post.summary }}</p>
                                {% else %}
                                    <p class="text-muted small mb-2">{{ post.content[:100] }}...</p>
                                {% endif %}
                                <div class="d-flex align-items-center text-muted small">
                                    <span class="me-3">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ post.created_at.strftime('%d/%m/%Y') }}
                                    </span>
                                    <span class="me-3">
                                        <i class="fas fa-comments me-1"></i>
                                        {{ post.comments|length }} bình luận
                                    </span>
                                    {% if post.updated_at > post.created_at %}
                                        <span class="badge bg-info">Đã cập nhật</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="ms-3">
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('edit_post', id=post.id) }}" class="btn btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if current_user.is_admin %}
                                        <button class="btn btn-outline-danger" onclick="deletePost({{ post.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Chưa có bài viết nào</h5>
                        <p class="text-muted">Hãy tạo bài viết đầu tiên của bạn!</p>
                        <a href="{{ url_for('create_post') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Tạo bài viết mới
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.post-item:last-child {
    border-bottom: none !important;
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
}
</style>
{% endblock %}
