{% extends "base.html" %}

{% block title %}Chỉnh sửa thông tin - Đ<PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-edit me-2"></i>Chỉnh sửa thông tin cá nhân
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.username.label(class="form-label") }}
                                    {{ form.username(class="form-control") }}
                                    <div class="form-text">Tên đăng nhập phải từ 4-20 ký tự</div>
                                    {% if form.username.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.username.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.email.label(class="form-label") }}
                                    {{ form.email(class="form-control") }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small">
                                            {% for error in form.email.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.full_name.label(class="form-label") }}
                            {{ form.full_name(class="form-control") }}
                            {% if form.full_name.errors %}
                                <div class="text-danger small">
                                    {% for error in form.full_name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>Hủy
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Current Info Display -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Thông tin hiện tại</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Tên đăng nhập:</strong><br>
                            <span class="text-muted">{{ current_user.username }}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>Email:</strong><br>
                            <span class="text-muted">{{ current_user.email }}</span>
                        </div>
                        <div class="col-md-4">
                            <strong>Họ và tên:</strong><br>
                            <span class="text-muted">{{ current_user.full_name }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Quyền:</strong>
                            {% if current_user.is_admin %}
                                <span class="badge bg-danger">Quản trị viên</span>
                            {% else %}
                                <span class="badge bg-secondary">Người dùng</span>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <strong>Ngày tạo:</strong><br>
                            <span class="text-muted">{{ current_user.created_at.strftime('%d/%m/%Y %H:%M') }}</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Security Notice -->
            <div class="alert alert-warning mt-3">
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Lưu ý bảo mật</h6>
                <ul class="mb-0">
                    <li>Thay đổi thông tin sẽ được ghi lại trong blockchain logs</li>
                    <li>Blockchain hash sẽ được cập nhật tự động</li>
                    <li>Không thể hoàn tác sau khi lưu thay đổi</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
