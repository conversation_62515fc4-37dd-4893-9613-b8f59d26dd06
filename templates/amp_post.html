<!doctype html>
<html ⚡ lang="vi">
<head>
    <meta charset="utf-8">
    <script async src="https://cdn.ampproject.org/v0.js"></script>
    <script async custom-element="amp-analytics" src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"></script>
    <script async custom-element="amp-social-share" src="https://cdn.ampproject.org/v0/amp-social-share-0.1.js"></script>
    <script async custom-element="amp-sidebar" src="https://cdn.ampproject.org/v0/amp-sidebar-0.1.js"></script>
    <title>{{ post.meta_title or post.title }}</title>
    <link rel="canonical" href="{{ url_for('post_detail', slug=post.slug, _external=True) }}">
    <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="{{ post.meta_description or post.summary }}">
    <meta name="keywords" content="{{ post.keywords }}">
    <meta name="author" content="{{ post.author.full_name or post.author.username }}">
    
    <!-- Open Graph -->
    <meta property="og:title" content="{{ post.meta_title or post.title }}">
    <meta property="og:description" content="{{ post.meta_description or post.summary }}">
    <meta property="og:type" content="article">
    <meta property="og:url" content="{{ url_for('post_detail', slug=post.slug, _external=True) }}">
    {% if post.featured_image %}
    <meta property="og:image" content="{{ url_for('uploaded_file', filename=post.featured_image, _external=True) }}">
    {% endif %}
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ post.meta_title or post.title }}">
    <meta name="twitter:description" content="{{ post.meta_description or post.summary }}">
    {% if post.featured_image %}
    <meta name="twitter:image" content="{{ url_for('uploaded_file', filename=post.featured_image, _external=True) }}">
    {% endif %}
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "headline": "{{ post.title }}",
        "description": "{{ post.meta_description or post.summary }}",
        "author": {
            "@type": "Person",
            "name": "{{ post.author.full_name or post.author.username }}"
        },
        "publisher": {
            "@type": "Organization",
            "name": "Phật Học Platform",
            "logo": {
                "@type": "ImageObject",
                "url": "{{ url_for('static', filename='images/logo.png', _external=True) }}"
            }
        },
        "datePublished": "{{ post.created_at.isoformat() }}",
        "dateModified": "{{ post.updated_at.isoformat() }}",
        {% if post.featured_image %}
        "image": "{{ url_for('uploaded_file', filename=post.featured_image, _external=True) }}",
        {% endif %}
        "mainEntityOfPage": "{{ url_for('post_detail', slug=post.slug, _external=True) }}"
    }
    </script>
    
    <style amp-boilerplate>body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}</style><noscript><style amp-boilerplate>body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}</style></noscript>
    
    <style amp-custom>
        /* AMP CSS - Optimized for performance */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            margin: 0;
            padding: 0;
            background: #fff;
        }

        .amp-container {
            max-width: 700px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Header */
        .amp-header {
            background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
            color: #333;
            padding: 12px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .amp-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .amp-logo {
            font-size: 18px;
            font-weight: 700;
            text-decoration: none;
            color: #333;
        }

        .amp-menu-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
        }

        /* Breadcrumb */
        .amp-breadcrumb {
            background: #f8f9fa;
            padding: 8px 0;
            font-size: 14px;
            border-bottom: 1px solid #e9ecef;
        }

        .amp-breadcrumb a {
            color: #007bff;
            text-decoration: none;
        }

        /* Article */
        .amp-article {
            padding: 24px 0;
        }

        .amp-category {
            background: {{ post.category.color }};
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .amp-title {
            font-size: 28px;
            font-weight: 800;
            margin: 16px 0;
            line-height: 1.2;
            color: #1a1a1a;
        }

        .amp-meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 24px;
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
        }

        .amp-meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .amp-featured-img {
            margin: 24px 0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .amp-content {
            font-size: 16px;
            line-height: 1.7;
            color: #333;
        }

        .amp-content h2 {
            font-size: 24px;
            font-weight: 700;
            margin: 32px 0 16px;
            color: #1a1a1a;
            border-bottom: 3px solid #ffc107;
            padding-bottom: 8px;
        }

        .amp-content h3 {
            font-size: 20px;
            font-weight: 600;
            margin: 24px 0 12px;
            color: #333;
        }

        .amp-content p {
            margin-bottom: 16px;
        }

        .amp-content blockquote {
            border-left: 4px solid #ffc107;
            background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
            padding: 16px 20px;
            margin: 24px 0;
            font-style: italic;
            border-radius: 0 8px 8px 0;
            position: relative;
        }

        .amp-content blockquote::before {
            content: '"';
            font-size: 48px;
            color: #ffc107;
            position: absolute;
            top: -8px;
            left: 16px;
            opacity: 0.3;
        }

        .amp-content ul, .amp-content ol {
            padding-left: 24px;
            margin-bottom: 16px;
        }

        .amp-content li {
            margin-bottom: 8px;
        }

        /* Tags */
        .amp-tags {
            margin: 32px 0;
            padding: 20px 0;
            border-top: 1px solid #e9ecef;
        }

        .amp-tag {
            background: #f8f9fa;
            color: #495057;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin: 4px;
            display: inline-block;
            border: 1px solid #e9ecef;
        }

        /* Social Share */
        .amp-social {
            margin: 32px 0;
            padding: 20px 0;
            border-top: 1px solid #e9ecef;
            text-align: center;
        }

        .amp-social h4 {
            margin-bottom: 16px;
            color: #333;
            font-size: 16px;
        }

        .amp-social-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* Footer */
        .amp-footer {
            background: #1a1a1a;
            color: #fff;
            text-align: center;
            padding: 32px 0;
            margin-top: 48px;
        }

        .amp-footer p {
            margin: 8px 0;
            font-size: 14px;
        }

        .amp-back-link {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            display: inline-block;
            margin: 16px 0;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        /* Sidebar */
        .amp-sidebar {
            background: #fff;
            padding: 20px;
        }

        .amp-sidebar h3 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
        }

        .amp-sidebar ul {
            list-style: none;
            padding: 0;
        }

        .amp-sidebar li {
            margin-bottom: 12px;
        }

        .amp-sidebar a {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .amp-title {
                font-size: 24px;
            }

            .amp-content {
                font-size: 15px;
            }

            .amp-container {
                padding: 0 12px;
            }

            .amp-meta {
                font-size: 13px;
            }

            .amp-social-buttons {
                gap: 8px;
            }
        }

        @media (max-width: 480px) {
            .amp-title {
                font-size: 20px;
            }

            .amp-content h2 {
                font-size: 20px;
            }

            .amp-container {
                padding: 0 8px;
            }
        }
    </style>
</head>
<body>
    <!-- AMP Sidebar -->
    <amp-sidebar id="sidebar" layout="nodisplay" side="left">
        <div class="amp-sidebar">
            <h3>📚 Menu</h3>
            <ul>
                <li><a href="{{ url_for('index', _external=True) }}">🏠 Trang chủ</a></li>
                <li><a href="{{ url_for('category_posts', slug=post.category.slug, _external=True) }}">📂 {{ post.category.name }}</a></li>
                <li><a href="{{ url_for('post_detail', slug=post.slug, _external=True) }}">💻 Phiên bản đầy đủ</a></li>
            </ul>
        </div>
    </amp-sidebar>

    <!-- Header -->
    <header class="amp-header">
        <div class="amp-container">
            <div class="amp-header-content">
                <a href="{{ url_for('index', _external=True) }}" class="amp-logo">
                    ⚡ Phật Học
                </a>
                <button class="amp-menu-btn" on="tap:sidebar.toggle">☰</button>
            </div>
        </div>
    </header>

    <!-- Breadcrumb -->
    <nav class="amp-breadcrumb">
        <div class="amp-container">
            <a href="{{ url_for('index', _external=True) }}">Trang chủ</a> ›
            <a href="{{ url_for('category_posts', slug=post.category.slug, _external=True) }}">{{ post.category.name }}</a> ›
            {{ post.title }}
        </div>
    </nav>

    <!-- Main Content -->
    <main class="amp-container">
        <article class="amp-article">
            <!-- Category Badge -->
            <div class="amp-category">
                {{ post.category.name }}
            </div>

            <!-- Title -->
            <h1 class="amp-title">{{ post.title }}</h1>

            <!-- Meta Information -->
            <div class="amp-meta">
                <div class="amp-meta-item">
                    <span>👤</span>
                    <span>{{ post.author.full_name or post.author.username }}</span>
                </div>
                <div class="amp-meta-item">
                    <span>📅</span>
                    <span>{{ post.created_at.strftime('%d/%m/%Y') }}</span>
                </div>
                <div class="amp-meta-item">
                    <span>👁️</span>
                    <span>{{ post.view_count }} lượt xem</span>
                </div>
                <div class="amp-meta-item">
                    <span>⏱️</span>
                    <span>{{ ((post.content|length / 200) | round) }} phút đọc</span>
                </div>
            </div>

            <!-- Featured Image -->
            {% if post.featured_image %}
            <div class="amp-featured-img">
                <amp-img src="{{ url_for('uploaded_file', filename=post.featured_image, _external=True) }}"
                         width="700" height="400" layout="responsive" alt="{{ post.title }}">
                </amp-img>
            </div>
            {% endif %}

            <!-- Article Content -->
            <div class="amp-content">
                {{ post.content|safe }}
            </div>

            <!-- Tags -->
            {% if post.keywords %}
            <div class="amp-tags">
                <h4>🏷️ Từ khóa liên quan:</h4>
                {% for keyword in post.keywords.split(',') %}
                    <span class="amp-tag">{{ keyword.strip() }}</span>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Social Share -->
            <div class="amp-social">
                <h4>📤 Chia sẻ bài viết</h4>
                <div class="amp-social-buttons">
                    <amp-social-share type="facebook" width="60" height="44"></amp-social-share>
                    <amp-social-share type="twitter" width="60" height="44"></amp-social-share>
                    <amp-social-share type="linkedin" width="60" height="44"></amp-social-share>
                    <amp-social-share type="email" width="60" height="44"></amp-social-share>
                </div>
            </div>

            <!-- Back to Full Version -->
            <div style="text-align: center; margin: 32px 0;">
                <a href="{{ url_for('post_detail', slug=post.slug, _external=True) }}" class="amp-back-link">
                    💻 Xem phiên bản đầy đủ
                </a>
            </div>
        </article>
    </main>

    <!-- AMP Analytics -->
    <amp-analytics type="gtag" data-credentials="include">
        <script type="application/json">
        {
            "vars": {
                "gtag_id": "GA_MEASUREMENT_ID",
                "config": {
                    "GA_MEASUREMENT_ID": {
                        "groups": "default"
                    }
                }
            },
            "triggers": {
                "trackPageview": {
                    "on": "visible",
                    "request": "pageview"
                }
            }
        }
        </script>
    </amp-analytics>

    <!-- Footer -->
    <footer class="amp-footer">
        <div class="amp-container">
            <p>&copy; 2024 Phật Học Platform. Tất cả quyền được bảo lưu.</p>
            <p>⚡ Trang AMP - Tối ưu cho mobile | Tải nhanh như chớp</p>
        </div>
    </footer>
</body>
</html>
