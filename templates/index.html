{% extends "base.html" %}

{% block title %}Trang chủ - Phật <PERSON>{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-75">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="display-4 fw-bold text-dark mb-4">
                        {{ home_content.hero_title }}
                    </h1>
                    <p class="lead text-muted mb-4">
                        {{ home_content.hero_subtitle }}
                    </p>

                    {% if not current_user.is_authenticated %}
                        <div class="hero-actions">
                            <a href="{{ url_for('login') }}" class="btn btn-warning btn-lg px-4">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                            </a>
                        </div>
                    {% else %}
                        <div class="hero-actions">
                            <a href="{{ url_for('create_post') }}" class="btn btn-warning btn-lg me-3 px-4">
                                <i class="fas fa-pen-fancy me-2"></i>Viết bài mới
                            </a>
                            <a href="#posts-section" class="btn btn-outline-dark btn-lg px-4">
                                <i class="fas fa-book-open me-2"></i>Đọc bài viết
                            </a>
                        </div>
                    {% endif %}

                    <div class="hero-stats mt-5">
                        <div class="row">
                            <div class="col-4">
                                <div class="stat-item">
                                    <h4 class="fw-bold text-warning mb-0">{{ total_posts }}</h4>
                                    <small class="text-muted">Bài viết</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h4 class="fw-bold text-warning mb-0">{{ total_categories }}</h4>
                                    <small class="text-muted">Danh mục</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="stat-item">
                                    <h4 class="fw-bold text-warning mb-0">{{ total_comments }}</h4>
                                    <small class="text-muted">Bình luận</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="hero-image text-center">
                    {% if home_content.hero_image %}
                        <img src="{{ url_for('uploaded_file', filename=home_content.hero_image) }}"
                             alt="Hero Image" class="img-fluid rounded shadow-lg" style="max-height: 400px;">
                    {% else %}
                        <div class="floating-elements">
                            <div class="floating-card">
                                <i class="fas fa-dharmachakra fa-3x text-warning mb-3"></i>
                                <h5>Pháp Luận</h5>
                                <p class="small text-muted">Giáo lý căn bản</p>
                            </div>
                            <div class="floating-card">
                                <i class="fas fa-lotus fa-3x text-info mb-3"></i>
                                <h5>Thiền Định</h5>
                                <p class="small text-muted">Tu tập nội tâm</p>
                            </div>
                            <div class="floating-card">
                                <i class="fas fa-hands-praying fa-3x text-success mb-3"></i>
                                <h5>Từ Bi</h5>
                                <p class="small text-muted">Tình thương vô biên</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Posts Section -->
<section id="posts-section" class="py-5 bg-white">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="section-header mb-4">
                    <h2 class="fw-bold text-dark">
                        <i class="fas fa-book-open text-warning me-2"></i>
                        {{ home_content.featured_posts_title }}
                    </h2>
                    <p class="text-muted">Khám phá những bài viết sâu sắc về Phật học</p>
                </div>

                {% if featured_posts %}
                    <div class="posts-grid">
                        {% for post in featured_posts %}
                        <article class="post-card-modern mb-4 animate-on-scroll" data-animation="fadeInUp" data-delay="{{ loop.index0 * 100 }}">
                            <div class="card border-0 shadow-sm h-100 post-card-hover">
                                <!-- Featured Image -->
                                {% if post.featured_image %}
                                <div class="card-img-container">
                                    <img src="{{ url_for('uploaded_file', filename=post.featured_image) }}"
                                         class="card-img-top" alt="{{ post.title }}"
                                         style="height: 250px; object-fit: cover;">
                                    <div class="image-overlay">
                                        <div class="category-badge" style="background-color: {{ post.category.color }};">
                                            <i class="{{ post.category.icon }} me-1"></i>{{ post.category.name }}
                                        </div>
                                        {% if post.is_featured %}
                                            <div class="featured-badge">
                                                <i class="fas fa-star"></i> Nổi bật
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}

                                <div class="card-body p-4">
                                    <!-- Category & Date -->
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div class="d-flex align-items-center">
                                            {% if not post.featured_image %}
                                            <span class="category-tag me-2" style="background-color: {{ post.category.color }};">
                                                <i class="{{ post.category.icon }} me-1"></i>{{ post.category.name }}
                                            </span>
                                            {% endif %}
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ post.created_at.strftime('%d/%m/%Y') }}
                                            </small>
                                        </div>
                                        {% if current_user.is_authenticated and (current_user.id == post.user_id or current_user.is_admin) %}
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-h"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{{ url_for('edit_post', id=post.id) }}">
                                                        <i class="fas fa-edit me-2"></i>Chỉnh sửa
                                                    </a></li>
                                                    {% if current_user.is_admin %}
                                                        <li><a class="dropdown-item text-danger" href="#" onclick="deletePost({{ post.id }})">
                                                            <i class="fas fa-trash me-2"></i>Xóa
                                                        </a></li>
                                                    {% endif %}
                                                </ul>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Post Title -->
                                    <h4 class="card-title mb-3 post-title">
                                        <a href="{{ url_for('post_detail', slug=post.slug) }}" class="text-decoration-none text-dark">
                                            {{ post.title }}
                                        </a>
                                    </h4>

                                    <!-- Post Summary -->
                                    {% if post.summary %}
                                        <p class="card-text text-muted mb-3 post-excerpt">{{ post.summary }}</p>
                                    {% else %}
                                        <p class="card-text text-muted mb-3 post-excerpt">{{ post.content[:200] }}...</p>
                                    {% endif %}

                                    <!-- Author Info -->
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="author-avatar me-2">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <small class="text-muted">
                                            Bởi <strong>{{ post.author.full_name or post.author.username }}</strong>
                                        </small>
                                    </div>

                                    <!-- Post Meta & Action -->
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div class="post-meta">
                                            <span class="badge bg-light text-dark me-2">
                                                <i class="fas fa-comments me-1"></i>{{ post.comments|selectattr('is_approved')|list|length }}
                                            </span>
                                            <span class="badge bg-light text-dark me-2">
                                                <i class="fas fa-eye me-1"></i>{{ post.view_count }}
                                            </span>
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-clock me-1"></i>{{ ((post.content|length / 200) | round) }} phút
                                            </span>
                                        </div>
                                        <a href="{{ url_for('post_detail', slug=post.slug) }}" class="btn btn-outline-warning btn-read-more">
                                            Đọc thêm <i class="fas fa-arrow-right ms-1"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </article>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state text-center py-5">
                        <i class="fas fa-book-open fa-4x text-muted mb-4"></i>
                        <h3 class="text-muted mb-3">Chưa có bài viết nào</h3>
                        <p class="text-muted mb-4">Hãy là người đầu tiên chia sẻ tri thức Phật học!</p>
                        {% if current_user.is_authenticated %}
                            <a href="{{ url_for('create_post') }}" class="btn btn-warning btn-lg">
                                <i class="fas fa-pen-fancy me-2"></i>Viết bài đầu tiên
                            </a>
                        {% else %}
                            <a href="{{ url_for('login') }}" class="btn btn-warning btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="sidebar">
                    <!-- Categories -->
                    <div class="widget mb-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0 fw-bold">
                                    <i class="fas fa-list me-2"></i>{{ home_content.categories_title }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="category-list">
                                    {% for category in categories %}
                                    <a href="{{ url_for('category_posts', slug=category.slug) }}"
                                       class="category-item d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="{{ category.icon }} me-2" style="color: {{ category.color }};"></i>
                                            {{ category.name }}
                                        </span>
                                        <span class="badge bg-light text-dark">
                                            {{ category.posts|selectattr('is_published')|list|length }}
                                        </span>
                                    </a>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quote of the Day -->
                    <div class="widget mb-4">
                        <div class="card border-0 shadow-sm bg-gradient-warning">
                            <div class="card-body text-center text-dark">
                                <i class="fas fa-quote-left fa-2x mb-3 opacity-50"></i>
                                <blockquote class="mb-3">
                                    "Hạnh phúc không phải là điều gì đó có sẵn. Nó đến từ hành động của chính bạn."
                                </blockquote>
                                <cite class="fw-bold">- Đức Đạt Lai Lạt Ma</cite>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="widget mb-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0 fw-bold">
                                    <i class="fas fa-clock me-2"></i>Hoạt động gần đây
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <div class="d-flex">
                                            <div class="activity-icon">
                                                <i class="fas fa-pen text-primary"></i>
                                            </div>
                                            <div class="activity-content">
                                                <p class="mb-1 small">Bài viết mới được đăng</p>
                                                <small class="text-muted">2 giờ trước</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="d-flex">
                                            <div class="activity-icon">
                                                <i class="fas fa-comment text-success"></i>
                                            </div>
                                            <div class="activity-content">
                                                <p class="mb-1 small">Bình luận mới</p>
                                                <small class="text-muted">5 giờ trước</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="activity-item">
                                        <div class="d-flex">
                                            <div class="activity-icon">
                                                <i class="fas fa-user-plus text-info"></i>
                                            </div>
                                            <div class="activity-content">
                                                <p class="mb-1 small">Thành viên mới tham gia</p>
                                                <small class="text-muted">1 ngày trước</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% if posts and posts.pages > 1 %}
<nav aria-label="Phân trang">
    <ul class="pagination justify-content-center">
        {% if posts.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('index', page=posts.prev_num) }}">Trước</a>
            </li>
        {% endif %}
        
        {% for page_num in posts.iter_pages() %}
            {% if page_num %}
                {% if page_num != posts.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('index', page=page_num) }}">{{ page_num }}</a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">…</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if posts.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('index', page=posts.next_num) }}">Sau</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
