{% extends "base.html" %}

{% block title %}Đăng nhập - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="row g-0 shadow-lg rounded-3 overflow-hidden">
            <!-- Left side - Login Form -->
            <div class="col-md-6">
                <div class="card h-100 border-0">
                    <div class="card-header bg-gradient-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                        </h3>
                        <p class="mb-0 mt-2 opacity-75">Chào mừng trở lại!</p>
                    </div>
                    <div class="card-body p-4">
                        <form method="POST" novalidate>
                            {{ form.hidden_tag() }}

                            <div class="mb-3">
                                {{ form.username.label(class="form-label fw-bold") }}
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-user text-muted"></i>
                                    </span>
                                    {{ form.username(class="form-control", placeholder="Nhập tên đăng nhập") }}
                                </div>
                                {% if form.username.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.username.errors %}
                                            <div><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-4">
                                {{ form.password.label(class="form-label fw-bold") }}
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="fas fa-lock text-muted"></i>
                                    </span>
                                    {{ form.password(class="form-control", placeholder="Nhập mật khẩu") }}
                                </div>
                                {% if form.password.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.password.errors %}
                                            <div><i class="fas fa-exclamation-circle me-1"></i>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="d-grid gap-2">
                                {{ form.submit(class="btn btn-primary btn-lg") }}
                            </div>

                            <div class="text-center mt-3">
                                <a href="{{ url_for('forgot_password') }}" class="text-decoration-none">
                                    <small><i class="fas fa-question-circle me-1"></i>Quên mật khẩu?</small>
                                </a>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer bg-light text-center py-3">
                        <small class="text-muted">
                            Tài khoản demo: <strong>admin / admin1234</strong>
                        </small>
                    </div>
                </div>
            </div>

            <!-- Right side - Welcome Message -->
            <div class="col-md-6">
                <div class="bg-gradient-secondary h-100 d-flex flex-column justify-content-center text-white p-4">
                    <div class="text-center">
                        <i class="fas fa-lotus fa-4x mb-4 opacity-75"></i>
                        <h4 class="mb-3">Chào mừng đến với</h4>
                        <h3 class="mb-3 fw-bold">Phật Học Platform</h3>
                        <p class="mb-4 opacity-75">
                            Nền tảng chia sẻ tri thức Phật giáo, nơi bạn có thể đọc và chia sẻ
                            những bài viết sâu sắc về triết lý và giáo lý Phật giáo.
                        </p>
                        <div class="alert alert-light text-dark">
                            <small>
                                <i class="fas fa-info-circle me-2"></i>
                                Liên hệ admin để được cấp tài khoản
                            </small>
                        </div>
                    </div>

                    <div class="mt-5">
                        <div class="row text-center">
                            <div class="col-4">
                                <i class="fas fa-book-open fa-2x mb-2 opacity-75"></i>
                                <small class="d-block">Đọc bài viết</small>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-edit fa-2x mb-2 opacity-75"></i>
                                <small class="d-block">Viết bài</small>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-comments fa-2x mb-2 opacity-75"></i>
                                <small class="d-block">Thảo luận</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
