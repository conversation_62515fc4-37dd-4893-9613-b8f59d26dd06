{% extends "base.html" %}

{% block title %}Blockchain Security Logs - Đạo <PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shield-alt text-primary"></i> Blockchain Security Logs</h2>
                <div>
                    <a href="{{ url_for('admin') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại Admin
                    </a>
                    <button onclick="verifyIntegrity()" class="btn btn-warning">
                        <i class="fas fa-check-circle"></i> Kiểm tra toàn vẹn dữ liệu
                    </button>
                </div>
            </div>

            <!-- Security Status Alert -->
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> Thông tin bảo mật Blockchain</h5>
                <p class="mb-0"><PERSON><PERSON> thống sử dụng công nghệ blockchain để đảm bảo tính toàn vẹn và bảo mật dữ liệu. 
                Mỗi hành động quan trọng được ghi lại với hash blockchain và chữ ký số.</p>
            </div>

            <!-- Logs Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i> Nhật ký hoạt động
                        <span class="badge badge-primary">{{ logs.total }} bản ghi</span>
                    </h5>
                </div>
                <div class="card-body">
                    {% if logs.items %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Thời gian</th>
                                    <th>Hành động</th>
                                    <th>Người dùng</th>
                                    <th>Chi tiết</th>
                                    <th>Blockchain Hash</th>
                                    <th>Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs.items %}
                                <tr>
                                    <td>
                                        <small>{{ log.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge badge-{% if log.action == 'ADMIN_LOGIN' %}success{% elif log.action == 'CREATE_USER' %}info{% elif log.action == 'CREATE_POST' %}primary{% else %}secondary{% endif %}">
                                            {{ log.action }}
                                        </span>
                                    </td>
                                    <td>
                                        <small>ID: {{ log.user_id }}</small>
                                    </td>
                                    <td>
                                        {% if log.details %}
                                        <small class="text-muted">{{ log.details[:50] }}{% if log.details|length > 50 %}...{% endif %}</small>
                                        {% else %}
                                        <small class="text-muted">-</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <code class="small">{{ log.blockchain_hash[:16] }}...</code>
                                    </td>
                                    <td>
                                        {% if log.integrity_status %}
                                        <span class="badge badge-success">
                                            <i class="fas fa-check"></i> Hợp lệ
                                        </span>
                                        {% else %}
                                        <span class="badge badge-danger">
                                            <i class="fas fa-exclamation-triangle"></i> Lỗi
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if logs.pages > 1 %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if logs.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('blockchain_logs', page=logs.prev_num) }}">Trước</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in logs.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != logs.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('blockchain_logs', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if logs.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('blockchain_logs', page=logs.next_num) }}">Sau</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Chưa có nhật ký nào được ghi lại.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Integrity Check Modal -->
<div class="modal fade" id="integrityModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-shield-alt"></i> Kết quả kiểm tra toàn vẹn dữ liệu
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="integrityResults">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Đang kiểm tra...</span>
                    </div>
                    <p class="mt-2">Đang kiểm tra toàn vẹn dữ liệu...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<script>
function verifyIntegrity() {
    $('#integrityModal').modal('show');
    
    fetch('{{ url_for("verify_data_integrity") }}')
        .then(response => response.json())
        .then(data => {
            let html = '<div class="row">';
            
            // Users
            html += `<div class="col-md-6 mb-3">
                <div class="card ${data.users.failed.length > 0 ? 'border-danger' : 'border-success'}">
                    <div class="card-header">
                        <h6><i class="fas fa-users"></i> Người dùng</h6>
                    </div>
                    <div class="card-body">
                        <p>Tổng: ${data.users.total} | Hợp lệ: ${data.users.verified}</p>
                        ${data.users.failed.length > 0 ? `<div class="alert alert-danger"><small>${data.users.failed.join('<br>')}</small></div>` : '<div class="alert alert-success">Tất cả hợp lệ</div>'}
                    </div>
                </div>
            </div>`;
            
            // Posts
            html += `<div class="col-md-6 mb-3">
                <div class="card ${data.posts.failed.length > 0 ? 'border-danger' : 'border-success'}">
                    <div class="card-header">
                        <h6><i class="fas fa-file-alt"></i> Bài viết</h6>
                    </div>
                    <div class="card-body">
                        <p>Tổng: ${data.posts.total} | Hợp lệ: ${data.posts.verified}</p>
                        ${data.posts.failed.length > 0 ? `<div class="alert alert-danger"><small>${data.posts.failed.join('<br>')}</small></div>` : '<div class="alert alert-success">Tất cả hợp lệ</div>'}
                    </div>
                </div>
            </div>`;
            
            // Comments
            html += `<div class="col-md-6 mb-3">
                <div class="card ${data.comments.failed.length > 0 ? 'border-danger' : 'border-success'}">
                    <div class="card-header">
                        <h6><i class="fas fa-comments"></i> Bình luận</h6>
                    </div>
                    <div class="card-body">
                        <p>Tổng: ${data.comments.total} | Hợp lệ: ${data.comments.verified}</p>
                        ${data.comments.failed.length > 0 ? `<div class="alert alert-danger"><small>${data.comments.failed.join('<br>')}</small></div>` : '<div class="alert alert-success">Tất cả hợp lệ</div>'}
                    </div>
                </div>
            </div>`;
            
            // Logs
            html += `<div class="col-md-6 mb-3">
                <div class="card ${data.logs.failed.length > 0 ? 'border-danger' : 'border-success'}">
                    <div class="card-header">
                        <h6><i class="fas fa-list"></i> Nhật ký</h6>
                    </div>
                    <div class="card-body">
                        <p>Tổng: ${data.logs.total} | Hợp lệ: ${data.logs.verified}</p>
                        ${data.logs.failed.length > 0 ? `<div class="alert alert-danger"><small>${data.logs.failed.join('<br>')}</small></div>` : '<div class="alert alert-success">Tất cả hợp lệ</div>'}
                    </div>
                </div>
            </div>`;
            
            html += '</div>';
            
            document.getElementById('integrityResults').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('integrityResults').innerHTML = 
                '<div class="alert alert-danger">Lỗi khi kiểm tra: ' + error + '</div>';
        });
}
</script>
{% endblock %}
