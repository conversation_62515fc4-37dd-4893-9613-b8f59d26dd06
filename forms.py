from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON><PERSON>ield, TextAreaField, PasswordField, SubmitField, SelectField, BooleanField
from wtforms.validators import DataRequired, Length, Email, EqualTo, Optional

class LoginForm(FlaskForm):
    username = String<PERSON>ield('Tên đăng nhập', validators=[DataRequired()])
    password = PasswordField('Mật khẩu', validators=[DataRequired()])
    submit = SubmitField('Đăng nhập')

class CreateUserForm(FlaskForm):
    username = String<PERSON>ield('Tên đăng nhập', validators=[
        DataRequired(),
        Length(min=4, max=20, message='Tên đăng nhập phải từ 4-20 ký tự')
    ])
    email = StringField('Email', validators=[
        DataRequired(),
        Email(message='<PERSON><PERSON> không hợp lệ')
    ])
    full_name = StringField('<PERSON><PERSON> và tên', validators=[
        DataRequired(),
        Length(min=2, max=100, message='Họ tên phải từ 2-100 ký tự')
    ])
    password = PasswordField('Mật khẩu', validators=[
        DataRequired(),
        Length(min=6, message='Mật khẩu phải ít nhất 6 ký tự')
    ])
    is_admin = BooleanField('Quyền quản trị')
    submit = SubmitField('Tạo tài khoản')

class CategoryForm(FlaskForm):
    name = StringField('Tên danh mục', validators=[DataRequired(), Length(max=100)])
    slug = StringField('Slug', validators=[DataRequired(), Length(max=100)])
    description = TextAreaField('Mô tả', validators=[Optional(), Length(max=500)])
    color = StringField('Màu sắc', validators=[Optional(), Length(max=7)], default='#ffc107')
    icon = StringField('Icon', validators=[Optional(), Length(max=50)], default='fas fa-book')
    submit = SubmitField('Lưu danh mục')

class PostForm(FlaskForm):
    title = StringField('Tiêu đề', validators=[DataRequired(), Length(min=5, max=200)])
    slug = StringField('Slug (URL)', validators=[Optional(), Length(max=200)])
    summary = StringField('Tóm tắt', validators=[Optional(), Length(max=300)])
    content = TextAreaField('Nội dung', validators=[DataRequired(), Length(min=10)])
    category_id = SelectField('Danh mục', coerce=int, validators=[DataRequired()])
    featured_image = FileField('Hình ảnh đại diện', validators=[
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Chỉ chấp nhận file ảnh!')
    ])
    meta_title = StringField('SEO Title', validators=[Optional(), Length(max=60)])
    meta_description = TextAreaField('SEO Description', validators=[Optional(), Length(max=160)])
    keywords = StringField('Keywords (phân cách bằng dấu phẩy)', validators=[Optional(), Length(max=255)])
    is_published = BooleanField('Xuất bản', default=True)
    is_featured = BooleanField('Bài viết nổi bật')
    submit = SubmitField('Lưu bài viết')

class CommentForm(FlaskForm):
    author_name = StringField('Tên của bạn', validators=[DataRequired(), Length(min=2, max=100)])
    content = TextAreaField('Bình luận', validators=[DataRequired(), Length(min=5, max=1000)])
    submit = SubmitField('Gửi bình luận')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('Mật khẩu hiện tại', validators=[DataRequired()])
    new_password = PasswordField('Mật khẩu mới', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Xác nhận mật khẩu mới',
                                   validators=[DataRequired(), EqualTo('new_password', message='Mật khẩu không khớp')])
    submit = SubmitField('Đổi mật khẩu')

class EditProfileForm(FlaskForm):
    username = StringField('Tên đăng nhập', validators=[DataRequired(), Length(min=4, max=20)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    full_name = StringField('Họ và tên', validators=[DataRequired(), Length(min=2, max=100)])
    submit = SubmitField('Cập nhật thông tin')

class ResetPasswordForm(FlaskForm):
    username = StringField('Tên đăng nhập', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    new_password = PasswordField('Mật khẩu mới', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Xác nhận mật khẩu mới',
                                   validators=[DataRequired(), EqualTo('new_password', message='Mật khẩu không khớp')])
    submit = SubmitField('Đặt lại mật khẩu')

class ForgotPasswordForm(FlaskForm):
    username = StringField('Tên đăng nhập', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    submit = SubmitField('Gửi yêu cầu đặt lại mật khẩu')
