from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_from_directory
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from datetime import datetime, timedelta
from config import Config
from models import db, User, Post, Comment, Category, BlockchainLog
from forms import LoginForm, CreateUserForm, PostForm, CommentForm, CategoryForm
from blockchain_security import blockchain_security
import markdown
import os
import secrets
from PIL import Image
from slugify import slugify
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.config.from_object(Config)

# Upload configuration
UPLOAD_FOLDER = 'static/uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create upload directory if it doesn't exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Vui lòng đăng nhập để truy cập trang này.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Context processor to make categories available in all templates
@app.context_processor
def inject_categories():
    categories = Category.query.all()
    return dict(categories=categories)

# Helper functions
def save_picture(form_picture, folder):
    """Save uploaded picture and return filename"""
    random_hex = secrets.token_hex(8)
    _, f_ext = os.path.splitext(form_picture.filename)
    picture_fn = random_hex + f_ext
    picture_path = os.path.join(app.root_path, 'static', folder, picture_fn)

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(picture_path), exist_ok=True)

    # Resize image
    output_size = (800, 600)
    img = Image.open(form_picture)
    img.thumbnail(output_size)
    img.save(picture_path)

    return picture_fn

def generate_slug(title):
    """Generate URL-friendly slug from title"""
    return slugify(title)

# Create tables and admin user
def create_tables():
    # Always drop and recreate tables to ensure correct schema
    db.drop_all()
    db.create_all()

    # Create admin user
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            full_name='Quản trị viên',
            is_admin=True
        )
        admin.set_password('admin1234')
        db.session.add(admin)
        db.session.commit()

        # Create blockchain log for admin creation
        log_entry = BlockchainLog()
        log_entry.create_from_action(
            action="CREATE_ADMIN_USER",
            user_id=admin.id,
            details={"username": "admin", "email": "<EMAIL>"}
        )
        db.session.add(log_entry)
        db.session.commit()

        # Create default categories
        default_categories = [
            {'name': 'Từ bi', 'slug': 'tu-bi', 'description': 'Bài viết về lòng từ bi và tình thương', 'color': '#dc3545', 'icon': 'fas fa-heart'},
            {'name': 'Trí tuệ', 'slug': 'tri-tue', 'description': 'Bài viết về trí tuệ và giác ngộ', 'color': '#0dcaf0', 'icon': 'fas fa-brain'},
            {'name': 'Thiền định', 'slug': 'thien-dinh', 'description': 'Bài viết về thiền định và tu tập', 'color': '#198754', 'icon': 'fas fa-leaf'},
            {'name': 'Tu tập', 'slug': 'tu-tap', 'description': 'Bài viết về phương pháp tu tập', 'color': '#ffc107', 'icon': 'fas fa-hands-praying'},
            {'name': 'Kinh điển', 'slug': 'kinh-dien', 'description': 'Giải thích các kinh điển Phật giáo', 'color': '#6f42c1', 'icon': 'fas fa-book-open'},
        ]

        for cat_data in default_categories:
            existing_cat = Category.query.filter_by(slug=cat_data['slug']).first()
            if not existing_cat:
                category = Category(**cat_data)
                db.session.add(category)

        db.session.commit()

        # Get default category for sample posts
        default_category = Category.query.filter_by(slug='tri-tue').first()

        # Create sample posts
        sample_posts = [
            {
                'title': 'Tứ Diệu Đế - Bốn Chân Lý Cao Quý',
                'summary': 'Tìm hiểu về Tứ Diệu Đế, nền tảng cơ bản của giáo lý Phật giáo về khổ đau và con đường giải thoát.',
                'content': '''# Tứ Diệu Đế - Bốn Chân Lý Cao Quý

Tứ Diệu Đế là nền tảng cơ bản của giáo lý Phật giáo, được Đức Phật thuyết giảng trong bài pháp đầu tiên tại Vườn Lộc Uyển.

## 1. Khổ Đế (Dukkha)
Chân lý về sự khổ đau. Cuộc sống đầy rẫy những khổ đau: sinh, lão, bệnh, tử, ái biệt ly, cầu bất đắc...

## 2. Tập Đế (Samudaya)
Chân lý về nguyên nhân của khổ đau. Khổ đau sinh ra từ tham ái, sân hận và si mê.

## 3. Diệt Đế (Nirodha)
Chân lý về sự chấm dứt khổ đau. Niết-bàn là trạng thái hoàn toàn giải thoát khỏi mọi khổ đau.

## 4. Đạo Đế (Magga)
Chân lý về con đường dẫn đến chấm dứt khổ đau. Đó chính là Bát Chánh Đạo.

> "Này các Tỳ-kheo, đây là Thánh đế về khổ: Sinh là khổ, già là khổ, bệnh là khổ, chết là khổ..."'''
            },
            {
                'title': 'Bát Chánh Đạo - Con Đường Tám Nhánh',
                'summary': 'Khám phá Bát Chánh Đạo, con đường tu tập tám nhánh dẫn đến giải thoát trong Phật giáo.',
                'content': '''# Bát Chánh Đạo - Con Đường Tám Nhánh

Bát Chánh Đạo là con đường tu tập gồm tám nhánh, được chia thành ba nhóm: Giới, Định, Tuệ.

## Nhóm Tuệ (Pañña)
1. **Chánh Kiến** - Hiểu biết đúng đắn về Tứ Diệu Đế
2. **Chánh Tư Duy** - Suy nghĩ đúng đắn, không tham, sân, si

## Nhóm Giới (Sīla)
3. **Chánh Ngữ** - Nói lời chân thật, không nói dối, không nói lời độc ác
4. **Chánh Nghiệp** - Hành động đúng đắn, không sát sinh, không trộm cắp
5. **Chánh Mạng** - Sinh sống bằng nghề nghiệp chính đáng

## Nhóm Định (Samādhi)
6. **Chánh Tinh Tần** - Nỗ lực đúng đắn trong tu tập
7. **Chánh Niệm** - Tỉnh giác, chánh niệm trong mọi hoạt động
8. **Chánh Định** - Thiền định đúng đắn

Con đường này dẫn đến sự giải thoát hoàn toàn khỏi khổ đau.'''
            }
        ]
        
        for post_data in sample_posts:
            # Check if post already exists
            existing_post = Post.query.filter_by(title=post_data['title']).first()
            if not existing_post:
                post = Post(
                    title=post_data['title'],
                    slug=slugify(post_data['title']),
                    summary=post_data['summary'],
                    content=post_data['content'],
                    meta_title=post_data['title'],
                    meta_description=post_data['summary'],
                    user_id=admin.id,
                    category_id=default_category.id if default_category else 1
                )
                # Create blockchain security for post
                post.create_blockchain_security()
                db.session.add(post)
        
        db.session.commit()

@app.route('/')
def index():
    page = request.args.get('page', 1, type=int)
    posts = Post.query.filter_by(is_published=True).order_by(Post.created_at.desc()).paginate(
        page=page, per_page=6, error_out=False
    )
    categories = Category.query.all()
    return render_template('index.html', posts=posts, categories=categories)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            # Verify user data integrity
            if not user.verify_integrity():
                flash('Cảnh báo: Dữ liệu tài khoản có thể đã bị thay đổi!', 'warning')

            login_user(user)
            flash('Đăng nhập thành công!', 'success')

            # Log admin login
            if user.is_admin:
                log_entry = BlockchainLog()
                log_entry.create_from_action(
                    action="ADMIN_LOGIN",
                    user_id=user.id,
                    details={"ip": request.remote_addr, "user_agent": request.headers.get('User-Agent')}
                )
                db.session.add(log_entry)
                db.session.commit()

            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash('Tên đăng nhập hoặc mật khẩu không đúng!', 'error')
    
    return render_template('login.html', form=form)

# Register route removed - only admin can create users

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Đã đăng xuất thành công!', 'success')
    return redirect(url_for('index'))

@app.route('/admin')
@login_required
def admin():
    if not current_user.is_admin:
        flash('Bạn không có quyền truy cập trang này!', 'error')
        return redirect(url_for('index'))
    
    # Statistics
    total_posts = Post.query.count()
    total_comments = Comment.query.count()
    pending_comments = Comment.query.filter_by(is_approved=False).count()
    
    # Recent posts (this week)
    week_ago = datetime.utcnow() - timedelta(days=7)
    recent_posts = Post.query.filter(Post.created_at >= week_ago).count()
    
    # Get recent posts and pending comments
    posts = Post.query.order_by(Post.created_at.desc()).limit(10).all()
    pending_comments_list = Comment.query.filter_by(is_approved=False).order_by(Comment.created_at.desc()).limit(5).all()
    
    return render_template('admin.html', 
                         total_posts=total_posts,
                         total_comments=total_comments,
                         pending_comments=pending_comments,
                         recent_posts=recent_posts,
                         posts=posts,
                         pending_comments_list=pending_comments_list)

@app.route('/profile')
@login_required
def profile():
    user_posts = Post.query.filter_by(user_id=current_user.id).order_by(Post.created_at.desc()).all()
    return render_template('profile.html', user_posts=user_posts)

@app.route('/create_post', methods=['GET', 'POST'])
@login_required
def create_post():
    form = PostForm()

    # Populate category choices
    categories = Category.query.all()
    form.category_id.choices = [(cat.id, cat.name) for cat in categories]

    if form.validate_on_submit():
        # Generate slug if not provided
        slug = form.slug.data if form.slug.data else generate_slug(form.title.data)

        # Handle image upload
        featured_image = None
        if form.featured_image.data:
            featured_image = save_picture(form.featured_image.data, 'uploads')

        post = Post(
            title=form.title.data,
            slug=slug,
            summary=form.summary.data,
            content=form.content.data,
            category_id=form.category_id.data,
            featured_image=featured_image,
            meta_title=form.meta_title.data or form.title.data,
            meta_description=form.meta_description.data or form.summary.data,
            keywords=form.keywords.data,
            is_published=form.is_published.data,
            is_featured=form.is_featured.data,
            user_id=current_user.id
        )

        # Create blockchain security for new post
        post.create_blockchain_security()

        db.session.add(post)
        db.session.commit()

        # Log post creation
        if current_user.is_admin:
            log_entry = BlockchainLog()
            log_entry.create_from_action(
                action="CREATE_POST",
                user_id=current_user.id,
                details={"post_id": post.id, "title": post.title}
            )
            db.session.add(log_entry)
            db.session.commit()

        flash('Đã tạo bài viết thành công!', 'success')
        return redirect(url_for('post_detail', slug=post.slug))

    return render_template('create_post.html', form=form)

@app.route('/edit_post/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_post(id):
    post = Post.query.get_or_404(id)

    # Check permissions
    if not (current_user.is_admin or current_user.id == post.user_id):
        flash('Bạn không có quyền sửa bài viết này!', 'error')
        return redirect(url_for('index'))

    # Populate category choices first
    categories = Category.query.all()

    if request.method == 'GET':
        # For GET request, populate form with existing data
        form = PostForm(obj=post)
        form.category_id.choices = [(cat.id, cat.name) for cat in categories]
        form.category_id.data = post.category_id
    else:
        # For POST request, create form normally
        form = PostForm()
        form.category_id.choices = [(cat.id, cat.name) for cat in categories]

    if form.validate_on_submit():
        # Update slug if title changed
        if form.title.data != post.title:
            new_slug = form.slug.data if form.slug.data else generate_slug(form.title.data)
            post.slug = new_slug

        # Handle image upload
        if form.featured_image.data:
            post.featured_image = save_picture(form.featured_image.data, 'uploads')

        post.title = form.title.data
        post.summary = form.summary.data
        post.content = form.content.data
        post.category_id = form.category_id.data
        post.meta_title = form.meta_title.data or form.title.data
        post.meta_description = form.meta_description.data or form.summary.data
        post.keywords = form.keywords.data
        post.is_published = form.is_published.data
        post.is_featured = form.is_featured.data
        post.updated_at = datetime.utcnow()

        # Update blockchain security for edited post
        post.create_blockchain_security()

        db.session.commit()

        # Log post edit
        if current_user.is_admin:
            log_entry = BlockchainLog()
            log_entry.create_from_action(
                action="EDIT_POST",
                user_id=current_user.id,
                details={"post_id": post.id, "title": post.title}
            )
            db.session.add(log_entry)
            db.session.commit()

        flash('Đã cập nhật bài viết thành công!', 'success')
        return redirect(url_for('post_detail', slug=post.slug))

    return render_template('create_post.html', form=form, post=post)

@app.route('/admin/users')
@login_required
def manage_users():
    if not current_user.is_admin:
        flash('Bạn không có quyền truy cập!', 'error')
        return redirect(url_for('index'))

    users = User.query.all()
    return render_template('admin_users.html', users=users)

@app.route('/admin/create_user', methods=['GET', 'POST'])
@login_required
def create_user():
    if not current_user.is_admin:
        flash('Bạn không có quyền truy cập!', 'error')
        return redirect(url_for('index'))

    form = CreateUserForm()
    if form.validate_on_submit():
        # Check if username already exists
        existing_user = User.query.filter_by(username=form.username.data).first()
        if existing_user:
            flash('Tên đăng nhập đã tồn tại!', 'error')
            return render_template('create_user.html', form=form)

        # Check if email already exists
        existing_email = User.query.filter_by(email=form.email.data).first()
        if existing_email:
            flash('Email đã được sử dụng!', 'error')
            return render_template('create_user.html', form=form)

        # Create new user
        user = User(
            username=form.username.data,
            email=form.email.data,
            full_name=form.full_name.data,
            is_admin=form.is_admin.data
        )
        user.set_password(form.password.data)
        db.session.add(user)
        db.session.commit()

        # Log user creation
        log_entry = BlockchainLog()
        log_entry.create_from_action(
            action="CREATE_USER",
            user_id=current_user.id,
            details={"new_user_id": user.id, "username": user.username, "is_admin": user.is_admin}
        )
        db.session.add(log_entry)
        db.session.commit()

        flash('Đã tạo tài khoản thành công!', 'success')
        return redirect(url_for('manage_users'))

    return render_template('create_user.html', form=form)

@app.route('/admin/categories')
@login_required
def manage_categories():
    if not current_user.is_admin:
        flash('Bạn không có quyền truy cập!', 'error')
        return redirect(url_for('index'))

    categories = Category.query.all()
    return render_template('admin_categories.html', categories=categories)

@app.route('/admin/create_category', methods=['GET', 'POST'])
@login_required
def create_category():
    if not current_user.is_admin:
        flash('Bạn không có quyền truy cập!', 'error')
        return redirect(url_for('index'))

    form = CategoryForm()
    if form.validate_on_submit():
        # Generate slug if not provided
        slug = form.slug.data if form.slug.data else generate_slug(form.name.data)

        # Check if slug already exists
        existing_cat = Category.query.filter_by(slug=slug).first()
        if existing_cat:
            flash('Slug đã tồn tại!', 'error')
            return render_template('create_category.html', form=form)

        category = Category(
            name=form.name.data,
            slug=slug,
            description=form.description.data,
            color=form.color.data,
            icon=form.icon.data
        )
        db.session.add(category)
        db.session.commit()

        flash('Đã tạo danh mục thành công!', 'success')
        return redirect(url_for('manage_categories'))

    return render_template('create_category.html', form=form)

@app.route('/post/<slug>')
def post_detail(slug):
    post = Post.query.filter_by(slug=slug, is_published=True).first_or_404()

    # Increase view count (only for non-bot requests)
    user_agent = request.headers.get('User-Agent', '').lower()
    if not any(bot in user_agent for bot in ['bot', 'crawler', 'spider', 'scraper']):
        post.view_count += 1
        db.session.commit()

    comment_form = CommentForm()

    # Get approved comments
    approved_comments = Comment.query.filter_by(post_id=post.id, is_approved=True).order_by(Comment.created_at.desc()).all()

    # Get related posts from same category
    related_posts = Post.query.filter(
        Post.id != post.id,
        Post.category_id == post.category_id,
        Post.is_published == True
    ).order_by(Post.created_at.desc()).limit(5).all()

    # If no related posts in same category, get from other categories
    if not related_posts:
        related_posts = Post.query.filter(
            Post.id != post.id,
            Post.is_published == True
        ).order_by(Post.created_at.desc()).limit(5).all()

    # Convert markdown to HTML
    post.content = markdown.markdown(post.content)

    # Check if mobile request for AMP
    is_mobile = 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent
    amp_param = request.args.get('amp')

    if amp_param == '1' or (is_mobile and request.args.get('amp') != '0'):
        return render_template('amp_post.html', post=post)

    return render_template('post_detail.html',
                         post=post,
                         comment_form=comment_form,
                         approved_comments=approved_comments,
                         related_posts=related_posts)

@app.route('/post/<slug>/amp')
def post_detail_amp(slug):
    post = Post.query.filter_by(slug=slug, is_published=True).first_or_404()

    # Convert markdown to HTML for AMP (clean HTML only)
    post.content = markdown.markdown(post.content, extensions=['extra'])

    # Clean content for AMP compliance
    import re
    # Remove any script tags
    post.content = re.sub(r'<script[^>]*>.*?</script>', '', post.content, flags=re.DOTALL)
    # Remove any style attributes
    post.content = re.sub(r'style="[^"]*"', '', post.content)
    # Replace img tags with amp-img
    post.content = re.sub(r'<img([^>]*)>', r'<amp-img\1 layout="responsive"></amp-img>', post.content)

    # Create response object properly
    from flask import make_response
    response = make_response(render_template('amp_post.html', post=post))

    # Set AMP headers
    response.headers['AMP-Access-Control-Allow-Source-Origin'] = request.headers.get('Origin', '')
    response.headers['Access-Control-Expose-Headers'] = 'AMP-Access-Control-Allow-Source-Origin'
    response.headers['Content-Type'] = 'text/html; charset=utf-8'

    return response

@app.route('/post/<slug>/comment', methods=['POST'])
def add_comment(slug):
    post = Post.query.filter_by(slug=slug).first_or_404()
    comment_form = CommentForm()

    if comment_form.validate_on_submit():
        comment = Comment(
            content=comment_form.content.data,
            author_name=comment_form.author_name.data,
            post_id=post.id,
            is_approved=False  # Comments need approval
        )

        # Create blockchain security for comment
        comment.create_blockchain_security()

        db.session.add(comment)
        db.session.commit()
        flash('Bình luận của bạn đã được gửi và đang chờ duyệt!', 'success')

    return redirect(url_for('post_detail', slug=slug))

@app.route('/admin/delete_post/<int:id>', methods=['POST'])
@login_required
def delete_post(id):
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Unauthorized'})

    post = Post.query.get_or_404(id)
    db.session.delete(post)
    db.session.commit()
    return jsonify({'success': True})

@app.route('/admin/delete_comment/<int:id>', methods=['POST'])
@login_required
def delete_comment(id):
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Unauthorized'})

    comment = Comment.query.get_or_404(id)
    db.session.delete(comment)
    db.session.commit()
    return jsonify({'success': True})

@app.route('/admin/approve_comment/<int:id>', methods=['POST'])
@login_required
def approve_comment(id):
    if not current_user.is_admin:
        return jsonify({'success': False, 'message': 'Unauthorized'})

    comment = Comment.query.get_or_404(id)
    comment.is_approved = True
    db.session.commit()
    return jsonify({'success': True})

# SEO Routes
@app.route('/sitemap.xml')
def sitemap():
    """Generate XML sitemap for SEO"""
    posts = Post.query.filter_by(is_published=True).all()
    categories = Category.query.all()

    sitemap_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>{}</loc>
        <changefreq>daily</changefreq>
        <priority>1.0</priority>
    </url>'''.format(url_for('index', _external=True))

    # Add posts
    for post in posts:
        sitemap_xml += '''
    <url>
        <loc>{}</loc>
        <lastmod>{}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>'''.format(
            url_for('post_detail', slug=post.slug, _external=True),
            post.updated_at.strftime('%Y-%m-%d')
        )

    # Add categories
    for category in categories:
        sitemap_xml += '''
    <url>
        <loc>{}</loc>
        <changefreq>weekly</changefreq>
        <priority>0.6</priority>
    </url>'''.format(url_for('category_posts', slug=category.slug, _external=True))

    sitemap_xml += '\n</urlset>'

    response = app.response_class(sitemap_xml, mimetype='application/xml')
    return response

@app.route('/robots.txt')
def robots():
    """Generate robots.txt for SEO"""
    robots_txt = '''User-agent: *
Allow: /
Disallow: /admin/
Disallow: /login
Sitemap: {}'''.format(url_for('sitemap', _external=True))

    response = app.response_class(robots_txt, mimetype='text/plain')
    return response

@app.route('/category/<slug>')
def category_posts(slug):
    """Display posts by category"""
    category = Category.query.filter_by(slug=slug).first_or_404()
    page = request.args.get('page', 1, type=int)
    posts = Post.query.filter_by(category_id=category.id, is_published=True)\
                     .order_by(Post.created_at.desc())\
                     .paginate(page=page, per_page=6, error_out=False)

    # Get all categories for sidebar
    all_categories = Category.query.all()

    return render_template('category_posts.html', posts=posts, category=category, categories=all_categories)

# Blockchain Security Routes
@app.route('/admin/blockchain_logs')
@login_required
def blockchain_logs():
    """View blockchain security logs"""
    if not current_user.is_admin:
        flash('Bạn không có quyền truy cập!', 'error')
        return redirect(url_for('index'))

    page = request.args.get('page', 1, type=int)
    logs = BlockchainLog.query.order_by(BlockchainLog.timestamp.desc())\
                             .paginate(page=page, per_page=20, error_out=False)

    # Verify integrity of logs
    for log in logs.items:
        log.integrity_status = log.verify_integrity()

    return render_template('blockchain_logs.html', logs=logs)

@app.route('/admin/verify_data_integrity')
@login_required
def verify_data_integrity():
    """Verify data integrity across the system"""
    if not current_user.is_admin:
        flash('Bạn không có quyền truy cập!', 'error')
        return redirect(url_for('index'))

    results = {
        'users': {'total': 0, 'verified': 0, 'failed': []},
        'posts': {'total': 0, 'verified': 0, 'failed': []},
        'comments': {'total': 0, 'verified': 0, 'failed': []},
        'logs': {'total': 0, 'verified': 0, 'failed': []}
    }

    # Verify users
    users = User.query.all()
    results['users']['total'] = len(users)
    for user in users:
        if user.verify_integrity():
            results['users']['verified'] += 1
        else:
            results['users']['failed'].append(f"User ID: {user.id} ({user.username})")

    # Verify posts
    posts = Post.query.all()
    results['posts']['total'] = len(posts)
    for post in posts:
        if post.verify_integrity():
            results['posts']['verified'] += 1
        else:
            results['posts']['failed'].append(f"Post ID: {post.id} ({post.title})")

    # Verify comments
    comments = Comment.query.all()
    results['comments']['total'] = len(comments)
    for comment in comments:
        if comment.verify_integrity():
            results['comments']['verified'] += 1
        else:
            results['comments']['failed'].append(f"Comment ID: {comment.id}")

    # Verify logs
    logs = BlockchainLog.query.all()
    results['logs']['total'] = len(logs)
    for log in logs:
        if log.verify_integrity():
            results['logs']['verified'] += 1
        else:
            results['logs']['failed'].append(f"Log ID: {log.id}")

    return jsonify(results)

# Image upload route for editor
@app.route('/upload_image', methods=['POST'])
@login_required
def upload_image():
    """Upload image for content editor"""
    print(f"Upload request from user: {current_user.username}")
    print(f"Request files: {list(request.files.keys())}")
    print(f"Request form: {dict(request.form)}")

    if 'image' not in request.files:
        print("No image file in request")
        return jsonify({'error': 'No image file'}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if file and allowed_file(file.filename):
        try:
            # Generate unique filename
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
            filename = timestamp + filename

            # Save original file
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(file_path)

            # Create thumbnail
            thumbnail_filename = 'thumb_' + filename
            thumbnail_path = os.path.join(app.config['UPLOAD_FOLDER'], thumbnail_filename)

            with Image.open(file_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')

                # Create thumbnail
                img.thumbnail((300, 300), Image.Resampling.LANCZOS)
                img.save(thumbnail_path, 'JPEG', quality=85)

                # Get image dimensions
                original_img = Image.open(file_path)
                width, height = original_img.size

            # Log image upload
            if current_user.is_admin:
                log_entry = BlockchainLog()
                log_entry.create_from_action(
                    action="UPLOAD_IMAGE",
                    user_id=current_user.id,
                    details={"filename": filename, "size": f"{width}x{height}"}
                )
                db.session.add(log_entry)
                db.session.commit()

            return jsonify({
                'success': True,
                'filename': filename,
                'thumbnail': thumbnail_filename,
                'url': url_for('uploaded_file', filename=filename),
                'thumbnail_url': url_for('uploaded_file', filename=thumbnail_filename),
                'width': width,
                'height': height
            })

        except Exception as e:
            return jsonify({'error': f'Upload failed: {str(e)}'}), 500

    return jsonify({'error': 'Invalid file type'}), 400

# Image resize route
@app.route('/resize_image', methods=['POST'])
@login_required
def resize_image():
    """Resize uploaded image"""
    data = request.get_json()
    filename = data.get('filename')
    width = data.get('width', 800)
    height = data.get('height', 600)
    quality = data.get('quality', 85)

    if not filename:
        return jsonify({'error': 'No filename provided'}), 400

    try:
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        if not os.path.exists(file_path):
            return jsonify({'error': 'File not found'}), 404

        # Create resized version
        resized_filename = f'resized_{width}x{height}_{filename}'
        resized_path = os.path.join(app.config['UPLOAD_FOLDER'], resized_filename)

        with Image.open(file_path) as img:
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')

            # Resize image
            img = img.resize((int(width), int(height)), Image.Resampling.LANCZOS)
            img.save(resized_path, 'JPEG', quality=int(quality))

        return jsonify({
            'success': True,
            'filename': resized_filename,
            'url': url_for('uploaded_file', filename=resized_filename)
        })

    except Exception as e:
        return jsonify({'error': f'Resize failed: {str(e)}'}), 500

# Upload route
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    with app.app_context():
        create_tables()
    app.run(debug=True)
